import logging
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List
from io import BytesIO

from services.custom_upload_files import CustomUploadFilesService
from services.custom_upload import AzureBlobService
from schema.custom_upload_schema import (
    CustomUploadFilesCreate,
    CustomUploadFilesUpdate,
    CustomUploadFilesResponse,
    CustomUploadFilesFilters,
    PaginatedCustomUploadFilesResponse,
)
from api.v1.deps import get_db, Transaction
from api.v1.wrappers import retry_request
from utility.validation import FileValidator

router = APIRouter()

blob_service = AzureBlobService()
custom_upload_service = CustomUploadFilesService()


def should_skip_validation(filename: str) -> bool:
    filename_lower = filename.lower()
    return "override" in filename_lower or "pricing" in filename_lower


# Add this at the top of your file (after imports)
DISTRIBUTOR_MAPPING = {
    "natura": "American Distributing",
    "anp": "Anipet Animal Supplies Inc.",
    "sar": "ASLLC",
    "cpt": "Central Pet",
    "greenies fauna": "Fauna Foods Corp",
    "ftg": "Focus Technology Group/AG Data",
    "nutro": "General Pet Supply",
    "vdb": "John A. Van Den Bosch Co.",
    "psl": "Pet Science Ltd.",
    "pfp": "Phillips Feed & Pet Supply",
    "zig": "Zeiglers Distributor",
}


def get_distributor_from_filename(filename: str) -> str:
    """
    Extract distributor name from filename based on known short forms
    """
    filename_lower = filename.lower()
    for short_name, full_name in DISTRIBUTOR_MAPPING.items():
        if short_name.lower() in filename_lower:
            return full_name
    return ""


@router.post("/upload/", response_model=CustomUploadFilesResponse)
async def upload_file(
    file: UploadFile = File(...),
    folder: str = Form(""),
    uploader_name: str = Form(...),
    db: Session = Depends(get_db),
):
    """
    Upload and validate a file to Azure Blob Storage and record in database
    """
    try:
        # Validate the file
        distributor_name = get_distributor_from_filename(file.filename)
        is_pfp = distributor_name == "Phillips Feed & Pet Supply"

        if should_skip_validation(file.filename):
            validation_result = {"is_valid": True, "errors": []}
        else:
            validator = FileValidator()
            validation_result = await validator.validate_file(file)

        # Process file upload
        file_size = 0
        file_content = await file.read()
        file_size = len(file_content)
        await file.seek(0)  # Reset file position
        # For PFP files, handle the fixed-width TXT to CSV conversion
        if is_pfp:
            # Get the original TXT content
            txt_content = file_content
            txt_blob_url = ""
            # Convert to CSV
            csv_content = await validator.convert_pfp_fixed_width_to_csv(file)
            # Upload both files to blob storage
            if file_size > 10 * 1024 * 1024:
                txt_blob_url = await blob_service.upload_large_file(file, folder)
            else:
                txt_blob_url = await blob_service.upload_file(file, folder)

            # Create CSV filename by changing extension
            csv_filename = file.filename[:-4] + ".csv"
            csv_blob_url = await blob_service.upload_file_from_bytes(
                csv_content, csv_filename, folder
            )

            # Use the TXT URL as the primary blob URL
            upload_result = txt_blob_url
        else:
            # Regular file upload
            if file_size > 10 * 1024 * 1024:
                upload_result = await blob_service.upload_large_file(file, folder)
            else:
                upload_result = await blob_service.upload_file(file, folder)
        error_blob_data = validation_result.get("error_blob_url", "")
        errorFileUrl = (
            error_blob_data["blobUrl"]
            if isinstance(error_blob_data, dict)
            else error_blob_data
        )
        blob_url = upload_result["blobUrl"]
        versioned_filename = upload_result["versionedFilename"]
        # Create file record with validation results
        file_details = CustomUploadFilesCreate(
            fileName=file.filename,  # Store original filename
            uploaderName=uploader_name,
            distributorName=distributor_name,
            fileType=file.content_type,
            fileSize=file.size,
            blobUrl=blob_url,
            isValidated=validation_result["is_valid"],
            validationErrors=validation_result.get("errors", []),
            errorFileUrl=errorFileUrl,
        )
        # Use the transaction context manager properly
        with Transaction(db) as session:
            result = custom_upload_service.create_or_update(session.db, file_details)
            if validation_result["is_valid"] and result.errorFileUrl:
                try:
                    # Extract blob name from URL
                    blob_name = result.errorFileUrl.split("/")[-1]
                    # Delete from blob storage
                    blob_service.delete_file(f"{blob_name}")
                    # Update DB to remove error file reference
                    result.errorFileUrl = ""
                    session.db.commit()
                except Exception as e:
                    # Log error but don't fail the request
                    logging.error(f"Failed to clean up error file: {str(e)}")
            return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# @router.post("/upload/", response_model=dict)
# async def upload_file(
#     file: UploadFile = File(...),
#     folder: str = Form(""),
#     uploader_name: str = Form(...),
#     distributor_name: str = Form(None),
#     db: Session = Depends(get_db),
# ):
#     """
#     Validate a file without uploading it
#     """
#     try:
#         # Validate the file
#         validator = FileValidator()
#         validation_result = await validator.validate_file(file)

#         if not validation_result["is_valid"]:
#             raise HTTPException(
#                 status_code=400,
#                 detail={
#                     "message": "File validation failed",
#                     "errors": validation_result,
#                 },
#             )

#         # Read file info without uploading
#         file_content = await file.read()
#         file_size = len(file_content)
#         await file.seek(0)  # Reset file position

#         return {
#             "status": "success",
#             "filename": file.filename,
#             "size": file_size,
#             "content_type": file.content_type,
#             "message": "File validation successful (not uploaded)",
#         }

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload-multiple/", response_model=List[CustomUploadFilesResponse])
# @retry_request(max_retries=5, delay=5)
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    folder: str = Form(""),
    uploader_name: str = Form(...),
    db: Session = Depends(get_db),
):
    """
    Upload and validate multiple files to Azure Blob Storage and record in database
    """
    with Transaction(db) as session:
        try:
            results = []
            validator = FileValidator()

            for file in files:
                try:
                    distributor_name = get_distributor_from_filename(file.filename)
                    is_pfp = distributor_name == "Phillips Feed & Pet Supply"

                    if should_skip_validation(file.filename):
                        validation_result = {"is_valid": True, "errors": []}
                    else:
                        validation_result = await validator.validate_file(file)

                    # Process file upload
                    file_size = 0
                    file_content = await file.read()
                    file_size = len(file_content)
                    await file.seek(0)  # Reset file position
                    if is_pfp:
                        # Get the original TXT content
                        txt_content = file_content
                        txt_blob_url = ""
                        # Convert to CSV
                        csv_content = await validator.convert_pfp_fixed_width_to_csv(
                            file_content
                        )

                        # Upload both files to blob storage
                        if file_size > 10 * 1024 * 1024:
                            txt_blob_url = await blob_service.upload_large_file(
                                file, folder
                            )
                        else:
                            txt_blob_url = await blob_service.upload_file(file, folder)

                        # Create CSV filename by changing extension
                        csv_filename = file.filename[:-4] + ".csv"
                        csv_blob_url = await blob_service.upload_file_from_bytes(
                            csv_content, csv_filename, folder
                        )

                        # Use the TXT URL as the primary blob URL
                        blob_url = txt_blob_url
                    else:
                        # Use chunked upload for large files
                        if file_size > 10 * 1024 * 1024:
                            blob_url = await blob_service.upload_large_file(
                                file, folder
                            )
                        else:
                            blob_url = await blob_service.upload_file(file, folder)
                    error_blob_data = validation_result.get("error_blob_url", "")
                    errorFileUrl = (
                        error_blob_data["blobUrl"]
                        if isinstance(error_blob_data, dict)
                        else error_blob_data
                    )
                    # Create file record with validation results
                    file_details = CustomUploadFilesCreate(
                        fileName=file.filename,
                        uploaderName=uploader_name,
                        distributorName=distributor_name,
                        fileType=file.content_type,
                        fileSize=file.size,
                        blobUrl=blob_url,
                        isValidated=validation_result["is_valid"],
                        validationErrors=validation_result.get("errors", []),
                        errorFileUrl=errorFileUrl,
                    )
                    # Use the transaction context manager properly
                    with Transaction(db) as session:
                        db_file = custom_upload_service.create_or_update(
                            session.db, file_details
                        )
                        # If validation passed and there was a previous error file, clean it up
                        if validation_result["is_valid"] and db_file.errorFileUrl:
                            try:
                                # Extract blob name from URL
                                blob_name = db_file.errorFileUrl.split("/")[-1]
                                # Delete from blob storage
                                blob_service.delete_file(f"{blob_name}")
                                # Update DB to remove error file reference
                                db_file.errorFileUrl = ""
                                session.db.commit()
                            except Exception as e:
                                # Log error but don't fail the request
                                logging.error(
                                    f"Failed to clean up error file: {str(e)}"
                                )
                        results.append(db_file)
                except HTTPException:
                    raise
            return results
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


# Update the list_files endpoint in the router
@router.get("/", response_model=PaginatedCustomUploadFilesResponse)
@retry_request(max_retries=5, delay=5)
def list_files(
    uploaderName: str = None,
    fileType: str = None,
    searchValue: str = None,
    limit: int = 20,
    page: int = 1,
    db: Session = Depends(get_db),
):
    """
    List files with optional filters.
    If uploader_name is provided, filter by uploader name.
    If file_type is provided, filter by file type.
    If search_query is provided, search in file names.
    Results are sorted by upload date (newest first) and updated date (newest first).
    """
    with Transaction(db) as session:
        files, total_count, validation_counts = (
            custom_upload_service.get_list(  # Unpack the tuple
                session.db,
                uploader_name=uploaderName,
                file_type=fileType,
                search_query=searchValue,
                limit=limit,
                page=page,
            )
        )
        return {
            "files": files,
            "totalCount": total_count,
            "validationCounts": validation_counts,
        }


@router.get("/{file_id}", response_model=CustomUploadFilesResponse)
@retry_request(max_retries=5, delay=5)
def get_file(file_id: int, db: Session = Depends(get_db)):
    """
    Get file details by ID
    """
    with Transaction(db) as session:
        file = custom_upload_service.get_file_by_id(session.db, file_id)
        if not file:
            raise HTTPException(status_code=404, detail="File not found")
        return file


@router.get("/download/{blob_name:path}")
@retry_request(max_retries=5, delay=5)
def download_file(blob_name: str):
    """
    Download a file from Azure Blob Storage
    """
    try:
        file_content = blob_service.download_file(blob_name)
        filename = blob_name.split("/")[-1]

        return StreamingResponse(
            BytesIO(file_content),
            media_type="application/octet-stream",
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list/")
def list_files_storage(
    folder: str = "",
):
    """
    List all files in a folder
    """
    try:
        files = blob_service.list_files(folder)
        return {"files": files}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{blob_name:path}")
def delete_file(blob_name: str, db: Session = Depends(get_db)):
    """
    Delete a file from Azure Blob Storage and database
    """
    try:
        # Delete from blob storage
        blob_result = blob_service.delete_file(blob_name)

        # Delete from database
        filename = blob_name.split("/")[-1]
        db_file = custom_upload_service.get_file_by_filename(filename)
        if db_file:
            custom_upload_service.delete_file(db_file.fileID)

        return {
            "success": blob_result,
            "message": f"File {blob_name} deleted successfully",
            "database_deleted": db_file is not None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{file_id}/history", response_model=List[CustomUploadFilesResponse])
def get_file_history(file_id: int, db: Session = Depends(get_db)):
    """Get version history for a file"""
    with Transaction(db) as session:
        history = custom_upload_service.get_file_history(session.db, file_id)
        if not history:
            raise HTTPException(
                status_code=404, detail="No history found for this file"
            )
        return history


@router.get(
    "/history/by-filename/{filename}", response_model=List[CustomUploadFilesResponse]
)
def get_file_history_by_filename(filename: str, db: Session = Depends(get_db)):
    """Get version history by filename"""
    with Transaction(db) as session:
        # Get current file first to get the ID
        current_file = custom_upload_service.get_file_by_filename(session.db, filename)
        if not current_file:
            raise HTTPException(status_code=404, detail="File not found")

        history = custom_upload_service.get_file_history(
            session.db, current_file.fileID
        )
        if not history:
            return [current_file]  # Return current version if no history

        # Combine current version with history
        history_response = []

        # Add current version first
        history_response.append(current_file)

        # Add historical versions
        history_response.extend(history)

        return history_response
