# Store Mapping Metadata Update API Documentation

## Overview
This document describes the new RESTful PUT API endpoint for updating store mapping metadata in the `nutrostore.tsStoreMap` table.

## Endpoint Details

### URL
```
PUT /api/v1/stores/mapping_unmapping/update_metadata
```

### Description
Updates one or more metadata fields for a store mapping record identified by VCID. Only the fields provided in the request payload will be updated. Empty strings are automatically converted to NULL values in the database.

### Authentication
This endpoint requires proper authentication (follows existing authentication patterns in the application).

## Request

### Headers
```
Content-Type: application/json
Authorization: Bearer <token>  # If authentication is required
```

### Request Body Schema
```json
{
  "vcid": integer (required),
  "customer_type": string (optional),
  "rep": string (optional),
  "address": string (optional),
  "warehouse": string (optional),
  "country": string (optional),
  "phone": string (optional)
}
```

### Field Descriptions
- **vcid** (integer, required): VCID (Mapping ID) to identify the record to update
- **customer_type** (string, optional): Customer type information
- **rep** (string, optional): Representative information
- **address** (string, optional): Store address
- **warehouse** (string, optional): Warehouse information
- **country** (string, optional): Store country
- **phone** (string, optional): Store phone number

### Field Mapping to Database
The API fields map to the following database columns in `nutrostore.tsStoreMap`:
- `customer_type` → `CustomerType`
- `rep` → `Rep`
- `address` → `DistStoreAddress`
- `warehouse` → `Warehouse`
- `country` → `Country`
- `phone` → `DistStorePhone`

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Store mapping metadata updated successfully",
  "updated_fields": ["customer_type", "rep", "address"],
  "vcid": 1232
}
```

### Response Field Descriptions
- **success** (boolean): Whether the update was successful
- **message** (string): Success or error message
- **updated_fields** (array): List of fields that were updated
- **vcid** (integer): VCID of the updated record

## Error Responses

### 400 Bad Request
Returned when no fields are provided for update.
```json
{
  "detail": "At least one field must be provided for update"
}
```

### 404 Not Found
Returned when the specified VCID doesn't exist in the database.
```json
{
  "detail": "Store mapping record with VCID 1232 not found"
}
```

### 422 Unprocessable Entity
Returned for validation errors (e.g., missing required fields, invalid data types).
```json
{
  "detail": [
    {
      "loc": ["body", "vcid"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 500 Internal Server Error
Returned for unexpected database or server errors.
```json
{
  "detail": "Unexpected error updating store mapping metadata: <error_message>"
}
```

## Usage Examples

### Example 1: Update All Fields
```bash
curl -X PUT "http://localhost:8000/api/v1/stores/mapping_unmapping/update_metadata" \
  -H "Content-Type: application/json" \
  -d '{
    "vcid": 1232,
    "customer_type": "Retail Chain Store",
    "rep": "John Smith",
    "address": "123 Main Street, New York, NY 10001",
    "warehouse": "East Coast Distribution Center",
    "country": "USA",
    "phone": "************"
  }'
```

### Example 2: Update Partial Fields
```bash
curl -X PUT "http://localhost:8000/api/v1/stores/mapping_unmapping/update_metadata" \
  -H "Content-Type: application/json" \
  -d '{
    "vcid": 1232,
    "customer_type": "Independent Pet Store",
    "phone": "************"
  }'
```

### Example 3: Clear Fields (Set to NULL)
```bash
curl -X PUT "http://localhost:8000/api/v1/stores/mapping_unmapping/update_metadata" \
  -H "Content-Type: application/json" \
  -d '{
    "vcid": 1232,
    "customer_type": "",
    "rep": ""
  }'
```

## Implementation Details

### Data Validation
- Empty strings and whitespace-only strings are converted to NULL
- Field length validation is handled by database constraints
- Special characters are properly escaped in SQL queries

### Transaction Management
- All updates are performed within a database transaction
- Automatic rollback on errors ensures data consistency
- Proper connection management and cleanup

### Error Handling
- Comprehensive error handling with appropriate HTTP status codes
- Detailed error messages for debugging
- Proper logging of all operations and errors

### Security Considerations
- SQL injection protection through parameterized queries
- Input validation and sanitization
- Follows existing authentication and authorization patterns

## Testing

### Unit Tests
Run the comprehensive test suite:
```bash
cd backend
python -m pytest tests/test_store_mapping_metadata_update.py -v
```

### Manual Testing
Use the provided manual test script:
```bash
cd backend
python test_mapping_metadata_manual.py
```

## Database Impact

### Table: nutrostore.tsStoreMap
The API updates the following columns:
- CustomerType
- Rep
- DistStoreAddress
- Warehouse
- Country
- DistStorePhone

### Performance Considerations
- Updates are performed using indexed VCID column for optimal performance
- Only specified fields are updated (no unnecessary column updates)
- Proper transaction isolation to prevent conflicts

## Monitoring and Logging

### Logging
All operations are logged with appropriate log levels:
- INFO: Successful operations
- WARNING: Validation issues
- ERROR: Database and unexpected errors

### Metrics
The API includes retry logic with configurable parameters:
- Maximum retries: 5
- Delay between retries: 5 seconds

## Version History

### Version 1.0
- Initial implementation
- Support for updating customer_type, rep, address, warehouse, country, phone fields
- Comprehensive error handling and validation
- Full test coverage
