from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID
from services.user_service import UserService, RoleService, UserRoleService
from schema.user_group_role_schema import (
    PaginatedUserListsResponse,
    UserCreate,
    UserListResponse,
    UserUpdate,
    UserResponse,
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    UserRoleCreate,
    UserRoleResponse,
)
from api.v1.deps import get_db, Transaction
from api.v1.wrappers import retry_request

router = APIRouter()

user_service = UserService()
role_service = RoleService()
user_role_service = UserRoleService()


# User endpoints
@router.get("/", response_model=PaginatedUserListsResponse)
@retry_request(max_retries=5, delay=5)
def get_users(
    page: int = 1,
    limit: int = 100,
    active: bool = None,
    user_type: str = None,
    search_query: str = None,
    db: Session = Depends(get_db),
):
    with Transaction(db) as session:
        user_list, totalCount = user_service.get_users(
            session.db,
            page=page,
            limit=limit,
            active=active,
            user_type=user_type,
            search_query=search_query,
        )
        return {"userList": user_list, "totalCount": totalCount}


@router.post("/", response_model=UserResponse)
@retry_request(max_retries=5, delay=5)
def create_user(user: UserCreate, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        return user_service.create_user(session.db, user)


@router.get("/{user_id}", response_model=UserResponse)
@retry_request(max_retries=5, delay=5)
def get_user(user_id: UUID, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        user = user_service.get_user(session.db, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user


@router.put("/{user_id}", response_model=UserResponse)
@retry_request(max_retries=5, delay=5)
def update_user(user_id: UUID, user: UserUpdate, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        return user_service.update_user(session.db, user_id, user)


@router.delete("/{user_id}")
@retry_request(max_retries=5, delay=5)
def delete_user(user_id: UUID, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        user_service.delete_user(session.db, user_id)
        return {"message": "User deleted successfully"}


@router.get("/managers/", response_model=List[UserListResponse])
@retry_request(max_retries=5, delay=5)
def get_users_list(db: Session = Depends(get_db)):
    with Transaction(db) as session:
        return user_service.get_users_list(
            session.db,
        )


# Role endpoints
@router.get("/roles/", response_model=List[RoleResponse])
@retry_request(max_retries=5, delay=5)
def get_roles(page: int = 1, limit: int = 100, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        return role_service.get_roles(session.db, page=page, limit=limit)


@router.post("/roles/", response_model=RoleResponse)
@retry_request(max_retries=5, delay=5)
def create_role(role: RoleCreate, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        return role_service.create_role(session.db, role)


@router.delete("/roles/{role_id}")
@retry_request(max_retries=5, delay=5)
def delete_role(role_id: UUID, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        role_service.delete_role(session.db, role_id)
        return {"message": "Role deleted successfully"}


@router.get("/roles/{role_id}", response_model=RoleResponse)
@retry_request(max_retries=5, delay=5)
def get_role(role_id: UUID, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        role = role_service.get_role(session.db, role_id)
        if not role:
            raise HTTPException(status_code=404, detail="Role not found")
        return role


@router.post("/{user_id}/roles/", response_model=UserRoleResponse)
@retry_request(max_retries=5, delay=5)
def assign_role_to_user(
    user_id: UUID, user_role: UserRoleCreate, db: Session = Depends(get_db)
):
    with Transaction(db) as session:
        return user_role_service.assign_role_to_user(
            session.db, UserRoleCreate(UserID=user_id, RoleID=user_role.RoleID)
        )


@router.get("/{user_id}/roles/", response_model=List[RoleResponse])
@retry_request(max_retries=5, delay=5)
def get_user_roles(user_id: UUID, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        return user_role_service.get_user_roles(session.db, user_id)


@router.delete("/{user_id}/roles/{role_id}")
@retry_request(max_retries=5, delay=5)
def remove_role_from_user(user_id: UUID, role_id: UUID, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        user_role_service.remove_role_from_user(session.db, user_id, role_id)
        return {"message": "Role removed from user successfully"}
