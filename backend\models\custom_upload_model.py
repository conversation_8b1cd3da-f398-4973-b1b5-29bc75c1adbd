import json
from typing import Any, Dict, List, Optional
from datetime import datetime
from sqlalchemy import ARRAY, String
from sqlmodel import Field, SQLModel
# from models.user_model import User


class CustomUploadFiles(SQLModel, table=True):
    """Model for storing file details in database"""

    fileID: Optional[int] = Field(default=None, primary_key=True)
    fileName: str = Field(max_length=255)
    uploaderName: str = Field(max_length=255)
    distributorName: Optional[str] = Field(default=None, max_length=255)
    fileType: str = Field(max_length=250)
    uploadDate: datetime = Field(default_factory=datetime.utcnow)
    fileSize: int
    blobUrl: str = Field(max_length=500)
    updatedDate: Optional[datetime] = Field(default=None)
    isValidated: bool = Field(default=False)
    validationErrors: Optional[str] = Field(default=None)  # Store as JSON string
    errorFileUrl: str = Field(max_length=500)
    # user_id: Optional[str] = Field(
    #     foreign_key="mpps.user.user_id", nullable=False, max_length=250
    # )

    __tablename__ = "customUploadFiles"
    __table_args__ = {"schema": "npddc_system"}

    class Config:
        from_attributes = True

    def set_validation_errors(self, errors: List[Dict[str, Any]]):
        self.validationErrors = json.dumps(errors) if errors else None

    def get_validation_errors(self) -> List[Dict[str, Any]]:
        return json.loads(self.validationErrors) if self.validationErrors else []


class CustomUploadFilesHistory(SQLModel, table=True):
    """Model for storing historical file versions"""

    historyID: Optional[int] = Field(default=None, primary_key=True)
    fileID: int = Field(foreign_key="npddc_system.customUploadFiles.fileID")
    fileName: str = Field(max_length=255)
    uploaderName: str = Field(max_length=255)
    distributorName: Optional[str] = Field(default=None, max_length=255)
    fileType: str = Field(max_length=250)
    uploadDate: datetime = Field(default_factory=datetime.utcnow)
    fileSize: int
    blobUrl: str = Field(max_length=500)
    updatedDate: Optional[datetime] = Field(default=None)
    isValidated: bool = Field(default=False)
    validationErrors: Optional[str] = Field(default=None)  # Store as JSON string
    errorFileUrl: str = Field(max_length=500)
    versionTimestamp: datetime = Field(default_factory=datetime.utcnow)

    __tablename__ = "customUploadFilesHistory"
    __table_args__ = {"schema": "npddc_system"}

    class Config:
        from_attributes = True

    def set_validation_errors(self, errors: List[Dict[str, Any]]):
        self.validationErrors = json.dumps(errors) if errors else None

    def get_validation_errors(self) -> List[Dict[str, Any]]:
        return json.loads(self.validationErrors) if self.validationErrors else []
