from datetime import timezone
from fastapi import Depends
from typing import Optional
from datetime import datetime, timedelta
from fastapi.security import OAuth<PERSON>P<PERSON>word<PERSON>earer
from passlib.context import CryptContext
import models
import services
from utility.exceptions_utility import get_user_exception
from jose import jwt, J<PERSON><PERSON>rror
from typing import Dict, Optional, Union

SECRET = "ipxktny"
ALGORITHM = "HS256"
bcrypt_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_bearer = OAuth2PasswordBearer(tokenUrl="token")


def get_password_hash(password):
    return bcrypt_context.hash(password)


def verify_password(plain_password, hashed_password):
    return bcrypt_context.verify(plain_password, hashed_password)

def create_access_token(
        username: str, user_id: int, expires_delta: Optional[timedelta] = None
):
    encode = {"sub": username, "id": user_id}
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    encode.update({"exp": expire})
    return jwt.encode(encode, SECRET, algorithm=ALGORITHM)


def get_current_user(token: str = Depends(oauth2_bearer)):
    try:
        payload = jwt.decode(token, SECRET, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        user_id: int = payload.get("id")
        if username is None or user_id is None:
            raise get_user_exception()
        return {"username": username, "id": user_id, "token": token}
    except JWTError as e:
        raise get_user_exception() from e


def check_user_valid(token: str):
    try:
        payload = jwt.decode(token, SECRET, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        user_id: int = payload.get("id")
        return username is not None and user_id is not None
    except JWTError as e:
        raise get_user_exception() from e

# For testing purpose comminting the code
def get_user_name(msal_token: dict, session) -> Union[Dict[str, Optional[str]], bool]:
    """
    Retrieve user information based on the MSAL token.
    If the user does not exist, create a new user.
    
    Args:
        msal_token (dict): The token containing user information.
        session: Database session object.
    
    Returns:
        dict: User details if found or created, otherwise False.
    """
    username = msal_token.get('preferred_username')
    
    user = services.users.get_users_data(session, user_name=username)
    
    if user:
        return format_user_response(user)
    
    curent_date = datetime.now(timezone.utc)
    curent_date =curent_date.isoformat()
    user_data = {
        "user_id": username,
        "region" : '',
        "role": 'Regional Reps',
        "is_active": 1,
        "created_on": curent_date,
        "modified_on": curent_date
    }

    new_user = create_user(user_data, session)
    
    return format_user_response(new_user) if new_user else False

def create_user(user_data: Dict[str, Union[str, int]], session) -> Optional[models.User]:
    """
    Create a new user in the database.
    
    Args:
        user_data (dict): The data for the new user.
        session: Database session object.
    
    Returns:
        User: The created user object or None if creation failed.
    """
    return services.users.create_user(session, obj_in=user_data)

def format_user_response(user: models.User) -> Dict[str, Optional[str]]:
    """
    Format user data into a response dictionary.
    
    Args:
        user (User): The user object.
    
    Returns:
        dict: Formatted user response.
    """
    return {
        "id": user.id,
        "username": user.user_id,
        "role": user.role
    }