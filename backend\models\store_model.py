from typing import Optional
from sqlmodel import Field, SQLModel


class Store(SQLModel, table=True):
    """Store model for nutrostore schema - represents store search results"""
    __tablename__ = "store_search_results"  # This is just for SQLModel, we'll use stored procedure
    __table_args__ = {"schema": "nutrostore"}
    
    NutroStoreID: Optional[str] = Field(default=None, max_length=50, primary_key=True)
    ChainName: Optional[str] = Field(default=None, max_length=50)
    StoreName: Optional[str] = Field(default=None, max_length=50)
    StoreNumber: Optional[str] = Field(default=None, max_length=50)
    Address: Optional[str] = Field(default=None, max_length=50)
    City: Optional[str] = Field(default=None, max_length=50)
    State: Optional[str] = Field(default=None, max_length=50)
    ZipCode: Optional[str] = Field(default=None, max_length=50)
    Phone: Optional[str] = Field(default=None, max_length=50)
    TerritoryDivID: Optional[str] = Field(default=None, max_length=10)

    class Config:
        from_attributes = True
