# services/user_service.py
from datetime import datetime
from typing import List, Optional
from uuid import UUID
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from sqlmodel import or_
from models.user_group_role_model import User, Role, UserRole
from schema.user_group_role_schema import (
    UserCreate,
    UserUpdate,
    RoleCreate,
    RoleUpdate,
    UserRoleCreate,
)
from sqlalchemy.orm import joinedload
import bcrypt


class UserService:
    def get_user(self, db: Session, user_id: UUID) -> Optional[User]:
        return db.query(User).filter(User.UserID == user_id).first()

    def get_user_by_login(self, db: Session, login: str) -> Optional[User]:
        return db.query(User).filter(User.UserLogin == login).first()

    def get_user_by_email(self, db: Session, email: str) -> Optional[User]:
        return db.query(User).filter(User.UserEmail.ilike(email)).first()

    def get_users(
        self,
        db: Session,
        limit: int = 20,
        page: int = 1,
        active: Optional[bool] = None,
        user_type: Optional[str] = None,
        search_query: Optional[str] = None,
    ) -> List[User]:
        query = db.query(User).options(
            joinedload(User.roles)  # Eager load the roles relationship
        )
        if active is not None:
            query = query.filter(User.Active == active)
        if user_type:
            query = query.filter(User.UserType == user_type)

        # Apply search query if provided
        if search_query:
            search_query = f"%{search_query}%"
            query = query.filter(
                or_(
                    User.UserLogin.ilike(search_query),
                    User.UserFullName.ilike(search_query),
                    User.UserEmail.ilike(search_query),
                    User.MarsUsername.ilike(search_query),
                )
            )
        totalCount = query.count()
        user_list = (
            query.order_by(User.UserID).offset((page - 1) * limit).limit(limit).all()
        )
        return user_list, totalCount

    def create_user(self, db: Session, user: UserCreate) -> User:
        # Check if user already exists
        if self.get_user_by_login(db, user.UserLogin):
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User with this login already exists",
            )

        # Check for existing email
        if self.get_user_by_email(db, user.UserEmail):
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User with this email already exists",
            )

        # Hash the password before storing
        # hashed_password = self._hash_password(user.UserPass)

        # Create user dict without password and then add the hashed one
        user_data = user.dict(exclude={"UserPass"})
        # user_data["UserPass"] = hashed_password

        db_user = User(**user_data)
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user

    def update_user(self, db: Session, user_id: UUID, user: UserUpdate) -> User:
        db_user = self.get_user(db, user_id)
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")

        update_data = user.dict(exclude_unset=True)

        # Always check for duplicate login (even if not changed)
        if "UserLogin" in update_data:
            existing_user = (
                db.query(User)
                .filter(
                    User.UserLogin == update_data["UserLogin"],
                    User.UserID != user_id,  # Exclude current user
                )
                .first()
            )
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="User with this login already exists",
                )

        # Always check for duplicate email (even if not changed)
        if "UserEmail" in update_data:
            existing_user = (
                db.query(User)
                .filter(
                    User.UserEmail == update_data["UserEmail"],
                    User.UserID != user_id,  # Exclude current user
                )
                .first()
            )
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="User with this email already exists",
                )

        # Update all fields
        for key, value in update_data.items():
            setattr(db_user, key, value)

        db.commit()
        db.refresh(db_user)
        return db_user

    def delete_user(self, db: Session, user_id: UUID) -> None:
        db_user = self.get_user(db, user_id)
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")
        db.delete(db_user)
        db.commit()

    # def authenticate_user(self, db: Session, login: str, password: str) -> Optional[User]:
    #     user = self.get_user_by_login(db, login)
    #     if not user:
    #         return None
    #     if not self._verify_password(password, user.UserPass):
    #         return None
    #     return user

    def get_users_list(
        self,
        db: Session,
    ) -> List[dict]:
        """
        Get simplified user list with ID, login, and full name
        """
        query = db.query(User.UserID, User.UserLogin, User.UserFullName)

        return query.order_by(User.UserLogin).all()

    def _hash_password(self, password: str) -> str:
        """Hash a password and return it as a UTF-8 string."""
        password_bytes = password.encode("utf-8")
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password_bytes, salt)
        return hashed.decode("utf-8")

    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a stored password against one provided by user"""
        return bcrypt.checkpw(
            plain_password.encode("utf-8"), hashed_password.encode("utf-8")
        )


class RoleService:
    def get_role(self, db: Session, role_id: UUID) -> Optional[Role]:
        return db.query(Role).filter(Role.RoleID == role_id).first()

    def get_roles(self, db: Session, page: int = 1, limit: int = 100) -> List[Role]:
        return db.query(Role).order_by(Role.RoleID).offset(page).limit(limit).all()

    def create_role(self, db: Session, role: RoleCreate) -> Role:
        db_role = Role(**role.dict())
        db.add(db_role)
        db.commit()
        db.refresh(db_role)
        return db_role

    def update_role(self, db: Session, role_id: UUID, role: RoleUpdate) -> Role:
        db_role = self.get_role(db, role_id)
        if not db_role:
            raise HTTPException(status_code=404, detail="Role not found")

        update_data = role.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_role, key, value)

        db.commit()
        db.refresh(db_role)
        return db_role

    def delete_role(self, db: Session, role_id: UUID) -> None:
        db_role = self.get_role(db, role_id)
        if not db_role:
            raise HTTPException(status_code=404, detail="Role not found")
        db.delete(db_role)
        db.commit()


class UserRoleService:
    def assign_role_to_user(self, db: Session, user_role: UserRoleCreate) -> UserRole:
        # First remove any existing roles for this user
        db.query(UserRole).filter(UserRole.UserID == user_role.UserID).delete()

        # Now assign the new role
        db_user_role = UserRole(**user_role.dict())
        db.add(db_user_role)
        db.commit()
        db.refresh(db_user_role)
        return db_user_role

    def remove_role_from_user(self, db: Session, user_id: UUID, role_id: UUID) -> None:
        db_user_role = (
            db.query(UserRole)
            .filter(UserRole.UserID == user_id, UserRole.RoleID == role_id)
            .first()
        )
        if not db_user_role:
            raise HTTPException(
                status_code=404, detail="User-Role relationship not found"
            )
        db.delete(db_user_role)
        db.commit()

    def get_user_roles(self, db: Session, user_id: UUID) -> List[Role]:
        user = db.query(User).filter(User.UserID == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user.roles
