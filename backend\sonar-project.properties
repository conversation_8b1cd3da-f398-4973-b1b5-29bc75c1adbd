# Required properties
sonar.projectKey=MPPS-Webapp-Backend
sonar.projectName=MPPS_Webapp_Backend

# Comma-separated list of source directories
sonar.sources=backend

# Language of the source code
sonar.language=python

# SonarQube server URL
sonar.host.url=https://sonarqube.effem.com/

# coverage file path
# sonar.python.coverage.reportPaths=backend/coverage.xml

# Additional properties can be added here based on the analysis needs

# Configure which files to include in the analysis
sonar.inclusions=**/*.py
# sonar.cpd.exclusions=**/tests/**
sonar.exclusions=**/antenv/**, **/tests/**
# Include test files in the analysis
# Exclude some test directories from the analysis
# sonar.coverage.exclusions=**/migrations/**,**/admin.py,**/apps.py,core/asgi.py,core/wsgi.py,manage.py

# Exclude C/C++/Objective-C files from analysis
sonar.c.file.suffixes=-
sonar.cpp.file.suffixes=-
sonar.objc.file.suffixes=-

sonar.qualitygate.wait=true

sonar.qualitygate.timeout=600