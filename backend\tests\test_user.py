import pytest
from datetime import datetime
from uuid import UUID, uuid4
from fastapi.testclient import TestClient
from fastapi.encoders import jsonable_encoder
import sys
import os
import random
import string

# Adjust path to import main app
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from main import app
from schema.user_group_role_schema import (
    UserCreate, UserUpdate, RoleCreate, RoleUpdate, UserRoleCreate
)

# Initialize test client
client = TestClient(app)
BASE_URL = "/api/v1/users"

# Global variables to track created test data
created_users = []
created_roles = []
created_user_roles = []

def generate_random_string(length=8):
    """Generate a random string of fixed length"""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for _ in range(length))

def get_test_user_data():
    """Generate unique test user data"""
    random_str = generate_random_string(6)
    return {
        "UserLogin": f"testuser_{random_str}",
        # "UserPass": "testpassword",
        "UserFirstName": "Test",
        "UserLastName": "User",
        "UserFullName": "Test User",
        "UserEmail": f"test_{random_str}@example.com",
        "MarsUsername": f"T{random.randint(10000, 99999)}",
        "UserType": "Tester",
        "Active": True
    }

def get_test_role_data():
    """Generate unique test role data"""
    return {
        "RoleName": f"TestRole_{generate_random_string(6)}",
        "RoleDescription": "Test Role Description"
    }

@pytest.fixture(scope="function")
def mock_user():
    return UserCreate(**get_test_user_data())

@pytest.fixture(scope="function")
def mock_role():
    return RoleCreate(**get_test_role_data())

@pytest.fixture(scope="function")
def mock_user_role():
    return UserRoleCreate(UserID=None, RoleID=None)

@pytest.fixture(scope="function")
def created_user(mock_user):
    """Fixture to create a test user and clean up after"""
    print("🟢 Creating test user")
    payload = jsonable_encoder(mock_user)
    response = client.post(f"{BASE_URL}/", json=payload)
    assert response.status_code == 200, f"Failed to create user: {response.text}"
    user_id = UUID(response.json()["UserID"])
    created_users.append(user_id)
    yield user_id

@pytest.fixture(scope="function")
def created_role(mock_role):
    """Fixture to create a test role and clean up after"""
    print("🟢 Creating test role")
    payload = jsonable_encoder(mock_role)
    response = client.post(f"{BASE_URL}/roles/", json=payload)
    assert response.status_code == 200, f"Failed to create role: {response.text}"
    role_id = UUID(response.json()["RoleID"])
    created_roles.append(role_id)
    yield role_id

@pytest.fixture(scope="function")
def created_user_role(created_user, created_role):
    """Fixture to create a test user-role relationship"""
    print("🟢 Creating test user-role relationship")
    payload = {"UserID": str(created_user), "RoleID": str(created_role)}
    response = client.post(f"{BASE_URL}/{created_user}/roles/", json=payload)
    assert response.status_code == 200, f"Failed to assign role: {response.text}"
    user_role_id = (created_user, created_role)
    created_user_roles.append(user_role_id)
    yield user_role_id

@pytest.fixture(scope="session", autouse=True)
def cleanup_after_tests():
    """Fixture to clean up all test data after all tests are done."""
    yield  # Let all tests run first
    
    # Clean up user-role relationships first
    print("\n🔴 Cleaning up ALL test user-role relationships")
    for user_id, role_id in created_user_roles:
        try:
            response = client.delete(f"{BASE_URL}/{user_id}/roles/{role_id}")
            print(f"Deleted user-role {user_id}/{role_id} - status: {response.status_code}")
        except Exception as e:
            print(f"Error deleting user-role {user_id}/{role_id}: {str(e)}")
    
    # Clean up roles
    print("\n🔴 Cleaning up ALL test roles")
    for role_id in created_roles:
        try:
            response = client.delete(f"{BASE_URL}/roles/{role_id}")
            print(f"Deleted role {role_id} - status: {response.status_code}")
        except Exception as e:
            print(f"Error deleting role {role_id}: {str(e)}")
    
    # Clean up users
    print("\n🔴 Cleaning up ALL test users")
    for user_id in created_users:
        try:
            response = client.delete(f"{BASE_URL}/{user_id}")
            print(f"Deleted user {user_id} - status: {response.status_code}")
        except Exception as e:
            print(f"Error deleting user {user_id}: {str(e)}")
    
    # Clear all tracking lists
    created_users.clear()
    created_roles.clear()
    created_user_roles.clear()

# User tests
def test_create_user():
    """Test creating a user with unique credentials"""
    user_data = get_test_user_data()
    payload = jsonable_encoder(UserCreate(**user_data))
    response = client.post(f"{BASE_URL}/", json=payload)
    assert response.status_code == 200
    assert response.json()["UserLogin"] == user_data["UserLogin"]
    user_id = UUID(response.json()["UserID"])
    created_users.append(user_id)

def test_create_duplicate_user(created_user):
    """Test that creating a duplicate user fails"""
    # Get the existing user's data
    response = client.get(f"{BASE_URL}/{created_user}")
    existing_user = response.json()
    
    # Try to create a new user with the same login
    duplicate_data = {
        "UserLogin": existing_user["UserLogin"],
        # "UserPass": "anotherpassword",
        "UserFirstName": "Duplicate",
        "UserLastName": "User",
        "UserEmail": "<EMAIL>"
    }
    response = client.post(f"{BASE_URL}/", json=duplicate_data)
    assert response.status_code == 400
    assert "User with this login already exists" in response.json()["detail"]

def test_get_users(created_user):
    """Test getting all users"""
    response = client.get(f"{BASE_URL}/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert any(user["UserID"] == str(created_user) for user in response.json())

def test_get_users_with_filters(created_user):
    """Test getting users with filters"""
    # Test active filter
    response = client.get(f"{BASE_URL}/?active=true")
    assert response.status_code == 200
    assert all(user["Active"] is True for user in response.json())
    
    # Test search query
    response = client.get(f"{BASE_URL}/?search_query=test")
    assert response.status_code == 200
    assert len(response.json()) > 0

def test_get_user(created_user):
    """Test getting a specific user"""
    response = client.get(f"{BASE_URL}/{created_user}")
    assert response.status_code == 200
    assert response.json()["UserID"] == str(created_user)
    assert response.json()["UserLogin"].startswith("testuser_")

def test_update_user(created_user):
    """Test updating a user"""
    update_data = {
        "UserFirstName": "Updated",
        "UserLastName": "Name",
        "UserEmail": "<EMAIL>"
    }
    response = client.put(f"{BASE_URL}/{created_user}", json=update_data)
    assert response.status_code == 200
    assert response.json()["UserFirstName"] == "Updated"
    assert response.json()["UserEmail"] == "<EMAIL>"

def test_delete_user(created_user):
    """Test deleting a user"""
    response = client.delete(f"{BASE_URL}/{created_user}")
    assert response.status_code == 200
    assert response.json() == {"message": "User deleted successfully"}
    if created_user in created_users:
        created_users.remove(created_user)

def test_get_user_not_found():
    """Test getting a non-existent user"""
    non_existent_id = uuid4()
    response = client.get(f"{BASE_URL}/{non_existent_id}")
    assert response.status_code == 404
    assert response.json() == {'detail': 'User not found'}

def test_get_users_list(created_user):
    """Test getting simplified users list"""
    response = client.get(f"{BASE_URL}/managers/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert any(user["UserID"] == str(created_user) for user in response.json())

# Role tests
def test_create_role():
    """Test creating a role"""
    role_data = get_test_role_data()
    payload = jsonable_encoder(RoleCreate(**role_data))
    response = client.post(f"{BASE_URL}/roles/", json=payload)
    assert response.status_code == 200
    assert response.json()["RoleName"] == role_data["RoleName"]
    role_id = UUID(response.json()["RoleID"])
    created_roles.append(role_id)

def test_get_roles(created_role):
    """Test getting all roles"""
    response = client.get(f"{BASE_URL}/roles/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert any(role["RoleID"] == str(created_role) for role in response.json())

def test_get_role(created_role):
    """Test getting a specific role"""
    response = client.get(f"{BASE_URL}/roles/{created_role}")
    assert response.status_code == 200
    assert response.json()["RoleID"] == str(created_role)
    assert response.json()["RoleName"].startswith("TestRole_")

def test_get_role_not_found():
    """Test getting a non-existent role"""
    non_existent_id = uuid4()
    response = client.get(f"{BASE_URL}/roles/{non_existent_id}")
    assert response.status_code == 404
    assert response.json() == {'detail': 'Role not found'}

# User-Role relationship tests
def test_assign_role_to_user(created_user, created_role):
    """Test assigning a role to a user"""
    payload = {"UserID": str(created_user), "RoleID": str(created_role)}
    response = client.post(f"{BASE_URL}/{created_user}/roles/", json=payload)
    assert response.status_code == 200
    assert response.json()["UserID"] == str(created_user)
    assert response.json()["RoleID"] == str(created_role)
    user_role_id = (created_user, created_role)
    created_user_roles.append(user_role_id)

def test_get_user_roles(created_user_role):
    """Test getting roles for a user"""
    user_id, role_id = created_user_role
    response = client.get(f"{BASE_URL}/{user_id}/roles/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert any(role["RoleID"] == str(role_id) for role in response.json())

def test_remove_role_from_user(created_user_role):
    """Test removing a role from a user"""
    user_id, role_id = created_user_role
    response = client.delete(f"{BASE_URL}/{user_id}/roles/{role_id}")
    assert response.status_code == 200
    assert response.json() == {"message": "Role removed from user successfully"}
    if (user_id, role_id) in created_user_roles:
        created_user_roles.remove((user_id, role_id))

def test_remove_nonexistent_role_from_user(created_user):
    """Test removing a non-existent role from a user"""
    non_existent_role_id = uuid4()
    response = client.delete(f"{BASE_URL}/{created_user}/roles/{non_existent_role_id}")
    assert response.status_code == 404