from pathlib import Path
from pydantic_settings import BaseSettings


class ProjectSettings(BaseSettings):
    app_description: str = """
    
## FastAPI Backend

APIs offer CRUD functionalities to interact with the data layers of the application database. You will be able to:

"""
    app_name: str = "NPDDC"
    version: str = "v1"
    app_title: str = f"{app_name} - REST APIs"
    app_summary: str = f"{app_name} Backend Swagger Documentation"
    tag_data: list = [{
        "name": "Product",
        "description": "Read, Create, Edit, Delete Products",
    }]
    host: str = "localhost"
    port: int = 8000
    security: str = "http"

    server_uri: str = rf"{security}://{host}:{port}"
    download_uri: str = rf"{server_uri}/downloads/"

    base_dir: Path = Path(__file__).resolve().parent
    mock_dir: Path = base_dir / "mock"
    log_dir: Path = base_dir / "logs"
    # log_file_path: str
    db_file: Path = base_dir / "database.db"


project_settings = ProjectSettings(_env_file=".env.local")
