#!/usr/bin/env python3
"""
Manual test script for the store mapping metadata update API
This script demonstrates how to use the new PUT API endpoint
"""

import requests
import json
import sys

# API endpoint configuration
BASE_URL = "http://localhost:8000"  # Adjust if your server runs on different port
API_ENDPOINT = f"{BASE_URL}/api/v1/stores/mapping_unmapping/update_metadata"

def test_api_endpoint():
    """Test the store mapping metadata update API endpoint"""
    
    print("🚀 Testing Store Mapping Metadata Update API")
    print(f"Endpoint: {API_ENDPOINT}")
    print("-" * 60)
    
    # Test Case 1: Update all fields
    print("\n📝 Test Case 1: Update all fields")
    test_data_1 = {
        "vcid": 1232,
        "customer_type": "Retail Chain Store",
        "rep": "<PERSON> Smith",
        "address": "123 Main Street, New York, NY 10001",
        "warehouse": "East Coast Distribution Center",
        "country": "USA",
        "phone": "************"
    }
    
    try:
        response = requests.put(API_ENDPOINT, json=test_data_1)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Test Case 1: PASSED")
        else:
            print("❌ Test Case 1: FAILED")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test Case 1: FAILED - Connection Error: {e}")
    except Exception as e:
        print(f"❌ Test Case 1: FAILED - Error: {e}")
    
    # Test Case 2: Update partial fields
    print("\n📝 Test Case 2: Update partial fields")
    test_data_2 = {
        "vcid": 1232,
        "customer_type": "Independent Pet Store",
        "phone": "************"
    }
    
    try:
        response = requests.put(API_ENDPOINT, json=test_data_2)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Test Case 2: PASSED")
        else:
            print("❌ Test Case 2: FAILED")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test Case 2: FAILED - Connection Error: {e}")
    except Exception as e:
        print(f"❌ Test Case 2: FAILED - Error: {e}")
    
    # Test Case 3: Empty strings (should convert to NULL)
    print("\n📝 Test Case 3: Empty strings to NULL")
    test_data_3 = {
        "vcid": 1232,
        "customer_type": "",  # Empty string
        "rep": "   ",  # Whitespace only
        "address": "Valid Address"
    }
    
    try:
        response = requests.put(API_ENDPOINT, json=test_data_3)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Test Case 3: PASSED")
        else:
            print("❌ Test Case 3: FAILED")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test Case 3: FAILED - Connection Error: {e}")
    except Exception as e:
        print(f"❌ Test Case 3: FAILED - Error: {e}")
    
    # Test Case 4: Invalid VCID (should return 404)
    print("\n📝 Test Case 4: Invalid VCID (404 test)")
    test_data_4 = {
        "vcid": 999999,  # Non-existent VCID
        "customer_type": "Test Store"
    }
    
    try:
        response = requests.put(API_ENDPOINT, json=test_data_4)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 404:
            print("✅ Test Case 4: PASSED (404 as expected)")
        else:
            print("❌ Test Case 4: FAILED (Expected 404)")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test Case 4: FAILED - Connection Error: {e}")
    except Exception as e:
        print(f"❌ Test Case 4: FAILED - Error: {e}")
    
    # Test Case 5: No fields to update (should return 400)
    print("\n📝 Test Case 5: No fields to update (400 test)")
    test_data_5 = {
        "vcid": 1232
        # No other fields
    }
    
    try:
        response = requests.put(API_ENDPOINT, json=test_data_5)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 400:
            print("✅ Test Case 5: PASSED (400 as expected)")
        else:
            print("❌ Test Case 5: FAILED (Expected 400)")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test Case 5: FAILED - Connection Error: {e}")
    except Exception as e:
        print(f"❌ Test Case 5: FAILED - Error: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 Manual testing completed!")
    print("💡 Make sure your FastAPI server is running on the correct port")
    print("💡 Adjust the VCID values based on your actual database records")

if __name__ == "__main__":
    test_api_endpoint()
