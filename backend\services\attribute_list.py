from datetime import datetime
from typing import List, Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import and_
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlmodel import or_
from models.attribute_list_model import AttributeListItem
from schema.attribute_list_schema import (
    AttributeListItemCreate,
    AttributeListItemUpdate,
)


class AttributeListItemService:
    def get(self, db: Session, rec_id: int) -> Optional[AttributeListItem]:
        return (
            db.query(AttributeListItem)
            .filter(AttributeListItem.RecID == rec_id)
            .first()
        )

    def get_list_items(
        self,
        db: Session,
        limit: int = 20,
        page: int = 1,
        list_name: Optional[str] = None,
        search_query: Optional[str] = None,
    ) -> Tuple[List[AttributeListItem], int]:
        query = db.query(AttributeListItem)

        filters = []
        if list_name:
            filters.append(AttributeListItem.ListName == list_name)
        if search_query:
            search_filters = [
                AttributeListItem.ListName.ilike(f"%{search_query}%"),
                AttributeListItem.ItemName.ilike(f"%{search_query}%"),
            ]
            if filters:
                filters.append(or_(*search_filters))
            else:
                filters = [or_(*search_filters)]

        if filters:
            query = query.filter(and_(*filters))
        totalCount = query.count()
        query = query.order_by(
            AttributeListItem.ListName.asc(),
            AttributeListItem.ItemOrder.asc(),
            AttributeListItem.ItemName.asc(),
        )
        attribute_list = query.offset((page - 1) * limit).limit(limit).all()
        return attribute_list, totalCount

    def get_all_list_names(self, db: Session) -> List[str]:
        result = db.query(AttributeListItem.ListName).distinct().all()
        return [item[0] for item in result]

    def create(self, db: Session, obj_in: AttributeListItemCreate) -> AttributeListItem:
        # Check if item with same ListName and ItemName already exists
        existing_item = (
            db.query(AttributeListItem)
            .filter(
                and_(
                    AttributeListItem.ListName == obj_in.ListName,
                    AttributeListItem.ItemName == obj_in.ItemName,
                )
            )
            .first()
        )

        if existing_item:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Item with name '{obj_in.ItemName}' already exists in list '{obj_in.ListName}'",
            )

        obj_data = obj_in.dict()
        db_obj = AttributeListItem(**obj_data)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self, db: Session, rec_id: int, obj_in: AttributeListItemUpdate
    ) -> AttributeListItem:
        db_obj = (
            db.query(AttributeListItem)
            .filter(AttributeListItem.RecID == rec_id)
            .first()
        )
        if not db_obj:
            raise HTTPException(status_code=404, detail="List item not found")

        update_data = obj_in.dict(exclude_unset=True)

        # Check if the update would create a duplicate
        if "ItemName" in update_data or "ListName" in update_data:
            new_list_name = update_data.get("ListName", db_obj.ListName)
            new_item_name = update_data.get("ItemName", db_obj.ItemName)

            existing_item = (
                db.query(AttributeListItem)
                .filter(
                    and_(
                        AttributeListItem.ListName == new_list_name,
                        AttributeListItem.ItemName == new_item_name,
                        AttributeListItem.RecID != rec_id,  # Exclude current item
                    )
                )
                .first()
            )

            if existing_item:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Item with name '{new_item_name}' already exists in list '{new_list_name}'",
                )

        for key, value in update_data.items():
            setattr(db_obj, key, value)

        if hasattr(db_obj, "UpdatedDate"):
            db_obj.UpdatedDate = datetime.utcnow()

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete(self, db: Session, rec_id: int) -> None:
        db_obj = (
            db.query(AttributeListItem)
            .filter(AttributeListItem.RecID == rec_id)
            .first()
        )
        if not db_obj:
            raise HTTPException(status_code=404, detail="List item not found")

        db.delete(db_obj)
        db.commit()
