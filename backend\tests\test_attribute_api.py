import pytest
from datetime import datetime, timezone
from fastapi.testclient import TestClient
from fastapi.encoders import jsonable_encoder
import sys
import os

# Adjust path to import main app
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from main import app
from schema.attribute_list_schema import AttributeListItemCreate

# Initialize test client
client = TestClient(app)
BASE_URL = "/api/v1/attribute_list"

# Current UTC time in ISO format
current_date = datetime.now(timezone.utc).isoformat()

# Test data (all datetime fields are strings)
test_attribute_list_item = {
    "ListName": "TestList",
    "ItemName": "TestItem",
    "ItemValue": "TEST",
    "ItemDescription": "Test Description",
    "ItemOrder": 1,
    "ParentListName": "ParentList",
    "ParentItemValue": "PARENT",
    "CreatedDate": current_date,
    "UpdatedDate": current_date
}

# This will store all created RecIDs during tests
created_records = []

@pytest.fixture(scope="function")
def mock_attribute_list_item():
    return AttributeListItemCreate(**test_attribute_list_item)

@pytest.fixture(scope="function")
def created_attribute_list_item(mock_attribute_list_item):
    print("🟢 Creating test attribute list item (runs once)")
    payload = jsonable_encoder(mock_attribute_list_item)
    response = client.post(f"{BASE_URL}/", json=payload)
    assert response.status_code == 200, f"Failed to create item: {response.text}"
    rec_id = response.json()["RecID"]
    created_records.append(rec_id)  # Add to cleanup list
    yield rec_id  # Pass RecID to tests
    
    # No teardown here - we'll handle cleanup in the session fixture

@pytest.fixture(scope="session", autouse=True)
def cleanup_after_tests():
    """Fixture to clean up all test data after all tests are done."""
    yield  # This lets all tests run first
    
    # After all tests are done, clean up all created records
    print("\n🔴 Cleaning up ALL test records")
    for rec_id in created_records:
        try:
            response = client.delete(f"{BASE_URL}/{rec_id}")
            print(f"Deleted record {rec_id} - status: {response.status_code}")
        except Exception as e:
            print(f"Error deleting record {rec_id}: {str(e)}")
    created_records.clear()  # Clear the list after cleanup

def test_get_attribute_list_items(created_attribute_list_item):
    """Test that the created item exists in the GET response."""
    print(f"🔵 Testing GET with RecID: {created_attribute_list_item}")
    response = client.get(f"{BASE_URL}/")
    assert response.status_code == 200, f"GET request failed: {response.text}"
    
    items = response.json()
    print("heyyyyyyyyyyyyyyyyyyyyyyyy", items)
    assert isinstance(items, list), "Response is not a list"
    
    # Check if the created item is in the response
    found_item = next(
        (item for item in items if item["RecID"] == created_attribute_list_item),
        None
    )
    assert found_item is not None, "Created item not found in response"
    assert found_item["ItemName"] == "TestItem", "Incorrect item data"

def test_get_attribute_list_items_with_filters(created_attribute_list_item):
    """Test filtering by ListName and search_query."""
    response = client.get(f"{BASE_URL}/?list_name=TestList")
    assert response.status_code == 200
    assert all(item["ListName"] == "TestList" for item in response.json())

    response = client.get(f"{BASE_URL}/?search_query=Test")
    assert response.status_code == 200
    assert len(response.json()) > 0, "No items matched the search query"

def test_get_all_list_names(created_attribute_list_item):
    response = client.get(f"{BASE_URL}/list-names/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert "TestList" in response.json()

def test_get_list_item(created_attribute_list_item):
    response = client.get(f"{BASE_URL}/{created_attribute_list_item}")
    assert response.status_code == 200
    assert response.json()["RecID"] == created_attribute_list_item
    assert response.json()["ItemName"] == "TestItem"

def test_create_list_item(mock_attribute_list_item):
    payload = jsonable_encoder(mock_attribute_list_item)
    response = client.post(f"{BASE_URL}/", json=payload)
    print("heyyyyyyyyyyyyyyyy", response.json())
    assert response.status_code == 200
    assert response.json()["ItemName"] == "TestItem"
    assert response.json()["ItemValue"] == "TEST"
    assert "RecID" in response.json()
    created_records.append(response.json()["RecID"])  # Add to cleanup list

def test_update_list_item(created_attribute_list_item):
    update_data = {
        "ItemName": "UpdatedItem",
        "ItemValue": "UPDATED",
        "ItemDescription": "Updated description"
    }
    response = client.put(f"{BASE_URL}/{created_attribute_list_item}", json=update_data)
    assert response.status_code == 200
    assert response.json()["ItemName"] == "UpdatedItem"
    assert response.json()["ItemValue"] == "UPDATED"

def test_patch_list_item(created_attribute_list_item):
    patch_data = {
        "ItemOrder": 2
    }
    response = client.patch(f"{BASE_URL}/{created_attribute_list_item}", json=patch_data)
    assert response.status_code == 200
    assert response.json()["ItemOrder"] == 2
    assert response.json()["ItemName"] == "TestItem"

def test_delete_list_item(created_attribute_list_item):
    response = client.delete(f"{BASE_URL}/{created_attribute_list_item}")
    assert response.status_code == 200
    assert response.json() == {"message": "List item deleted successfully"}
    if created_attribute_list_item in created_records:
        created_records.remove(created_attribute_list_item)

def test_get_list_item_not_found():
    response = client.get(f"{BASE_URL}/999999")
    assert response.status_code == 404
    assert response.json() == {'detail': 'List item not found'}

def test_update_list_item_not_found():
    update_data = {
        "ItemName": "UpdatedItem"
    }
    response = client.put(f"{BASE_URL}/999999", json=update_data)
    assert response.status_code == 404
    assert response.json() == {'detail': 'List item not found'}

def test_delete_list_item_not_found():
    response = client.delete(f"{BASE_URL}/999999")
    assert response.status_code == 404
    assert response.json() == {'detail': 'List item not found'}