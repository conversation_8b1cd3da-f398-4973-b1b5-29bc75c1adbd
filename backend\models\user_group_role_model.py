from typing import Optional, List
from datetime import datetime
from uuid import UUID, uuid4
from sqlmodel import Field, SQLModel, Relationship

class UserRole(SQLModel, table=True):
    """User-Role relationship model"""
    __tablename__ = "UserRoles"
    __table_args__ = {"schema": "npddc_system"}
    
    UserID: UUID = Field(foreign_key="npddc_system.Users.UserID", primary_key=True)
    RoleID: UUID = Field(foreign_key="npddc_system.Roles.RoleID", primary_key=True)

class Role(SQLModel, table=True):
    """Role model for npddc_system schema"""
    __tablename__ = "Roles"
    __table_args__ = {"schema": "npddc_system"}
    
    RoleID: UUID = Field(default_factory=uuid4, primary_key=True)
    RoleName: str = Field(max_length=50)
    RoleDescription: Optional[str] = Field(default=None, max_length=100)
    
    users: List["User"] = Relationship(back_populates="roles", link_model=UserRole)

class User(SQLModel, table=True):
    """User model for npddc_system schema"""
    __tablename__ = "Users"
    __table_args__ = {"schema": "npddc_system"}
    
    UserID: UUID = Field(default_factory=uuid4, primary_key=True)
    UserLogin: str = Field(max_length=50)
    UserPass: Optional[str] = Field(default=None, max_length=50)
    UserFirstName: Optional[str] = Field(default=None, max_length=50)
    UserLastName: Optional[str] = Field(default=None, max_length=50)
    UserFullName: Optional[str] = Field(default=None, max_length=255)
    UserEmail: Optional[str] = Field(default=None, max_length=255)
    MarsUsername: Optional[str] = Field(default=None, max_length=50)
    UserType: Optional[str] = Field(default=None, max_length=50)
    ManagerID: Optional[UUID] = Field(default=None)
    IsLockedOut: Optional[bool] = Field(default=False)
    IsPassReset: Optional[bool] = Field(default=False)
    LastLoginDate: Optional[datetime] = Field(default=None)
    CreateDate: Optional[datetime] = Field(default_factory=datetime.utcnow)
    FailedPasswordCount: Optional[int] = Field(default=0)
    Active: bool = Field(default=True)
    
    roles: List[Role] = Relationship(back_populates="users", link_model=UserRole)