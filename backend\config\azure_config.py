import os
# from dotenv import load_dotenv

# # Load environment variables
# load_dotenv()


class Settings:
    # Azure Storage settings
    # AZURE_STORAGE_CONNECTION_STRING = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
    print("tenant----", os.environ.get("TENANT_ID"))
    print("tenant----", os.environ.get("AZURE_STORAGE_CONTAINER_NAME"))
    AZURE_TENANT_ID = os.environ.get("TENANT_ID")
    AZURE_CLIENT_ID = os.environ.get("AZURE_CLIENT_ID")
    AZURE_CLIENT_SECRET = os.environ.get("AZURE_CLIENT_SECRET")
    AZURE_STORAGE_ACCOUNT_URL = os.environ.get("AZURE_STORAGE_ACCOUNT_URL")
    AZURE_STORAGE_CONTAINER_NAME = os.environ.get("AZURE_STORAGE_CONTAINER_NAME")
    AZURE_USE_MSI = True

    # Authentication method
    # USE_CONNECTION_STRING = AZURE_STORAGE_CONNECTION_STRING is not None
    USE_SERVICE_PRINCIPAL = all(
        [
            AZURE_TENANT_ID,
            AZURE_CLIENT_ID,
            AZURE_CLIENT_SECRET,
            AZURE_STORAGE_ACCOUNT_URL,
        ]
    )


settings = Settings()
