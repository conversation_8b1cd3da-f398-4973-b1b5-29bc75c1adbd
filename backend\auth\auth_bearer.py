"""Auth Bearer"""
import os
import httpx
import json
import base64

from cryptography.hazmat.backends.openssl import backend
from fastapi import Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from services.login_service import check_user_valid
from utility.exceptions_utility import get_user_exception, token_exception_Oauth2
from jose import jwt, JWTError
from cryptography.x509 import load_der_x509_certificate

app_id = os.getenv("CLIENT_ID")
issuer = os.getenv('AUTHORITY')


def verify_jwt(jwtoken: str):
    """Verify JWT function"""
    is_token_valid: bool = False
    try:
        payload = check_user_valid(jwtoken)
    except Exception:
        payload = None
    if payload:
        is_token_valid = True
    return is_token_valid


async def get_discovery_keys(metadata):
    """
    The function asynchronously fetches all the available public
    keys for particular applications and return them in a dictionary format.
    The response of discovery_url is cached for subsequent requests.
    """
    # OpenID Connect discovery endpoint
    discovery_url = metadata.get('jwks_uri')
    if not discovery_url:
        discovery_url = "https://login.microsoftonline.com/2fc13e34-f03f-498b-982a-7cb446e25bc6/discovery/v2.0/keys"
    async with httpx.AsyncClient() as client:
        keys_response = await client.get(discovery_url)
    return keys_response.json().get('keys')


# This Function is to get the public key based on the Application
def extract_public_key(token: str, discovery_keys):
    """
    This function matches the security key from header of token against
    the all the public keys available for the application.
    If security key matches with any of public key then matching key returned
    in a decoded format as "public_key".
    """

    # Decode the header
    try:
        header, claims, signature = token.split(".")
    except ValueError as e:
        raise token_exception_Oauth2('token unavaliable') from e
        return None    
    header_as_dict = json.loads(base64.b64decode(header))

    # request the public keys and hold the function flow until we get the result
    # keys = await get_discovery_keys()
    keys = discovery_keys

    # Fetch the matching key from public keys
    header_kid = header_as_dict.get('kid')
    matching_key = next((key for key in keys if key.get('kid') == header_kid), None)

    if not matching_key:
        return None

    public_key = load_der_x509_certificate(base64.b64decode(matching_key["x5c"][0]), backend).public_key()
    return public_key


def decode(token: str, discovery_keys, metadata):
    """
    This function primarily validates the token against the
    permissions, signature and expiration along with the other
    token attributes.
    The "decode" function of "jwt" module from "jose" perform validation for signature,
    audience and expiration by default.
    For more info on jwt.decode visit https://github.com/mpdavis/python-jose/blob/master/jose/jwt.py#L211
    """
    public_key = extract_public_key(token, discovery_keys)
    if not public_key:
        return False
    alg_values = metadata.get('id_token_signing_alg_values_supported')
    if not alg_values:
        alg_values = ['RS256']
    try:
        decoded_token = jwt.decode(
            token,
            key=public_key,
            algorithms=alg_values,
            audience=app_id,
            issuer=f'{issuer}/v2.0',
        )
        return {
            "preferred_username": decoded_token['preferred_username'],
            "name": decoded_token['name']
        }
    except JWTError as e:
        raise get_user_exception() from e


async def get_meta_data():
    oidc_url = os.getenv('OID_CONF_URLOID_CONF_URL')
    async with httpx.AsyncClient() as client:
        keys_response = await client.get(oidc_url)
    return keys_response.json()


async def oauth_verify(token):
    metadata = await get_meta_data()
    discovery_keys = await get_discovery_keys(metadata)
    return decode(token, discovery_keys, metadata)


class JWTBearer(HTTPBearer):
    """class to implement authorization for a user"""
    def __init__(self, auto_error: bool = True):
        """JWT bearer class initialization"""
        super(JWTBearer, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super(
            JWTBearer, self
        ).__call__(request)
        if not credentials:
            raise get_user_exception()
        if credentials.scheme != "Bearer":
            raise get_user_exception()
        if not verify_jwt(credentials.credentials):
            raise get_user_exception()
        return credentials.credentials


jwtBearer = JWTBearer()
