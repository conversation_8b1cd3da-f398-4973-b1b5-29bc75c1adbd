from datetime import datetime
from typing import List, Optional
from sqlmodel import Field, SQLModel

class User(SQLModel, table=True):
    """User Model"""
    user_id: str = Field(default=None, max_length=255)
    region: str = Field(max_length=100)
    role: str = Field(max_length=100)
    is_active: bool = Field(default=True)
    id: Optional[int] = Field(default=None, primary_key=True)
    created_on: datetime = Field(default_factory=datetime.utcnow)
    modified_on: Optional[datetime] = Field(default=None)

    __tablename__ = 'user'

    class Config:
        from_attributes = True
