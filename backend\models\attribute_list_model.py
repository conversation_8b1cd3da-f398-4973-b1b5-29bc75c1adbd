from typing import Optional
from datetime import datetime
from sqlalchemy import UniqueConstraint
from sqlmodel import Field, SQLModel

class AttributeListItem(SQLModel, table=True):
    """List Items Model for nutrostore schema"""
    RecID: Optional[int] = Field(default=None, primary_key=True)
    ListName: str = Field(max_length=255)
    ItemName: str = Field(max_length=255)
    ItemValue: str = Field(max_length=255)
    ItemDescription: Optional[str] = Field(default=None, max_length=255)
    ItemOrder: Optional[int] = Field(default=None)
    ParentListName: Optional[str] = Field(default=None, max_length=255)
    ParentItemValue: Optional[str] = Field(default=None, max_length=255)
    CreatedDate: Optional[datetime] = Field(default_factory=datetime.utcnow)
    # UpdatedDate: Optional[datetime] = Field(default=None)

    __tablename__ = 'tlListItems'
    __table_args__ = (
        UniqueConstraint('ListName', 'ItemName', name='uq_listname_itemname'),
        {"schema": 'nutrostore'}
    )

    class Config:
        from_attributes = True