from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class AttributeListItemBase(BaseModel):
    ListName: str
    ItemName: str
    ItemValue: str
    ItemDescription: Optional[str] = Field(default=None)
    ItemOrder: Optional[int] = Field(default=None)
    ParentListName: Optional[str] = Field(default=None)
    ParentItemValue: Optional[str] = Field(default=None)


class AttributeListItemCreate(AttributeListItemBase):
    CreatedDate: Optional[datetime] = Field(default_factory=datetime.utcnow)
    UpdatedDate: Optional[datetime] = Field(default=None)


class AttributeListItemUpdate(BaseModel):
    ListName: Optional[str] = Field(default=None)
    ItemName: Optional[str] = Field(default=None)
    ItemValue: Optional[str] = Field(default=None)
    ItemDescription: Optional[str] = Field(default=None)
    ItemOrder: Optional[int] = Field(default=None)
    ParentListName: Optional[str] = Field(default=None)
    ParentItemValue: Optional[str] = Field(default=None)
    # UpdatedDate: Optional[datetime] = Field(default_factory=datetime.utcnow)


class AttributeListItemFilters(BaseModel):
    ListName: Optional[str] = Field(default=None)
    ItemName: Optional[str] = Field(default=None)
    ItemValue: Optional[str] = Field(default=None)
    ParentListName: Optional[str] = Field(default=None)
    ParentItemValue: Optional[str] = Field(default=None)


class AttributeListItemResponse(AttributeListItemBase):
    RecID: int
    CreatedDate: datetime
    # UpdatedDate: Optional[datetime] = Field(default=None)

    class Config:
        from_attributes = True


class PaginatedAttributeListsResponse(BaseModel):
    attributeList: List[AttributeListItemResponse]
    totalCount: int
