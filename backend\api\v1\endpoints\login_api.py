from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, Form, Request
from sqlmodel import Session
from jose import jwt

from api.v1.deps import get_db
from msal_login.msal_auth import authenticate_user
from services.login_service import get_user_name
from utility.exceptions_utility import token_exception
from api.v1.wrappers import retry_request

router = APIRouter()


class LoginPasswordRequestForm:
    def __init__(
        self,
        grant_type: str = Form(default=None, regex="password"),
        username: str = Form(default=None),
        password: str = Form(default=None),
        token: str = Form(default=None),
    ):
        """Login Password Request form"""
        self.grant_type = grant_type
        self.username = username
        self.password = password
        self.token = token


@router.post("/login")
# @retry_request(max_retries=5, delay=5)
async def user_login(
    request: Request,
    session: Session = Depends(get_db),
):
    """User Login api"""
    payload = await request.json()
    token_value = payload["token"]
    msal_token = await authenticate_user(token_value)
    user = get_user_name(msal_token, session)
    if not user:
        raise token_exception()
    return {
        "data": {
            "username": user["username"],
            "token": token_value,
            "user_id": user["id"],
            "role": user["role"],
            "name": msal_token["name"],
        },
    }
