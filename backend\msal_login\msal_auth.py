"""API created for auth"""
import os

from authlib.integrations.starlette_client import <PERSON>Auth, OAuthError
from cachetools import TTLCache
from dotenv import load_dotenv
from fastapi import APIRouter, Depends
from fastapi.security import OAuth2, OAuth2PasswordBearer
from starlette.config import Config
from starlette.requests import Request
from starlette.responses import HTMLResponse, RedirectResponse
from auth.auth_bearer import oauth_verify
from typing import Dict, Optional, Union

import services

router = APIRouter()

SCOPE = ["User.Read"]
oauth2_bearer = OAuth2PasswordBearer(tokenUrl="token")
load_dotenv()

config_data = {'MICROSOFT_CLIENT_ID': os.getenv('CLIENT_ID'),
               'MICROSOFT_CLIENT_SECRET': os.getenv('CLIENT_SECRET')}

config = Config(environ=config_data)
oauth = OAuth(config)
oauth.register(
    name='microsoft',
    server_metadata_url=os.getenv('OID_CONF_URLOID_CONF_URL'),
    client_kwargs={
        'scope': 'openid email profile'
    }
)
session_cache = TTLCache(maxsize=100, ttl=600)


@router.get("/login/sso", response_class=RedirectResponse)
async def login_sso(request: Request):
    """Login sso api"""
    print('calling sso Login')
    redirect_uri = os.getenv('SSO_REDIRECT_BACKEND_PATH')
    return await oauth.microsoft.authorize_redirect(request, redirect_uri)


@router.get("/", response_class=RedirectResponse)
async def login_token(request: Request):
    """Gets token for a user"""
    try:
        token = await oauth.microsoft.authorize_access_token(request)
    except OAuthError as error:
        return error
    request.session['id_token'] = token['id_token']
    request.session['userinfo'] = token['userinfo']
    return os.getenv('FRONTEND_URL') + '/login/?accounts=' + token['id_token']


async def authenticate_user(token):
    """Authenticates a valid user"""
    if token:
        user_info = await oauth_verify(token)
        return user_info
    return False

async def validate_user(token: str = Depends(oauth2_bearer)):
    """Authenticates a valid user"""
    if token:
        user_info = await oauth_verify(token)
        print(user_info, 'User data info')
        return user_info
    return None