from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import sqlalchemy
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
def get_engine(**db_config):
    """Get Engine method with Active Directory Service Principal authentication"""
    connection_string = (
        f"DRIVER={db_config.get('driver', 'ODBC Driver 17 for SQL Server')};"
        f"SERVER={db_config['server']};"
        f"DATABASE={db_config['database']};"
        f"UID={db_config['username']};"
        f"PWD={db_config['password']};"
        f"Authentication=ActiveDirectoryServicePrincipal"
    )
    
    connection_url = f"mssql+pyodbc:///?odbc_connect={sqlalchemy.engine.url.quote_plus(connection_string)}"
    
    engine = create_engine(
        connection_url,
        pool_size=10,
        max_overflow=20,
        pool_timeout=30,
        pool_recycle=1800,
        connect_args={"timeout": 60},
        echo=True  # Optional: Enable logging for SQLAlchemy engine
    )
    return engine

engine = get_engine(
    username=os.getenv('DB_USERNAME', 'NA'),
    password=os.getenv('DB_PASSWORD', 'NA'),
    server=os.getenv('DB_SERVER', 'NA'),
    database=os.getenv('DB_NAME', 'NA'),
    driver=os.getenv('DB_DRIVER', 'ODBC Driver 17 for SQL Server')
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)