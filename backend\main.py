import time
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.encoders import jsonable_encoder
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse
from api.v1.routers import app_router
from starlette.middleware.sessions import SessionMiddleware

from settings import project_settings

app = FastAPI(
    title=project_settings.app_title,
    description=project_settings.app_description,
    summary=project_settings.app_summary,
    version=project_settings.version,
    terms_of_service="http://example.com/terms/",
    openapi_tags=project_settings.tag_data,
)

app.include_router(app_router, prefix="/api/v1")

whitelist_urls = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5174",
    "https://npddcfeeus2devas.azurewebsites.net",
    "https://npddcbeeus2devas.azurewebsites.net",
    "https://nadisteus2devas.azurewebsites.net",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=whitelist_urls,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)
app.add_middleware(SessionMiddleware, secret_key="!secret")


def retry_operation(func, max_retries=5):
    for attempt in range(1, max_retries + 1):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries:
                raise e
            time.sleep(1)


@app.exception_handler(Exception)
async def unhandled_exception_handler(request: Request, err: Exception):
    try:
        retry_operation(lambda: request.method)
    except Exception as final_err:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": f"Failed to execute: {request.method}: {request.url}. "
                f"Error after retries: {final_err}"
            },
        )


@app.get("/test-error")
async def test_error_endpoint():
    def error_prone_operation():
        raise HTTPException(
            status_code=500, detail="Intentional error for testing retries"
        )

    try:
        retry_operation(error_prone_operation)
    except HTTPException as e:
        raise e


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": exc.errors(), "body": exc.body}),
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app", host=project_settings.host, port=project_settings.port, reload=True
    )
