from typing import Generator
from fastapi import Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er

from sessions import <PERSON>Local
from sqlmodel import Session
from sqlalchemy.orm import declarative_base

Base = declarative_base()

class Transaction:

    def __init__(self, db: Session):
        self.db = db

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.db.rollback()
        else:
            self.db.commit()


def get_db() -> Generator:
    db = None
    try:
        db = SessionLocal()
        yield db
    finally:
        if db: db.close()