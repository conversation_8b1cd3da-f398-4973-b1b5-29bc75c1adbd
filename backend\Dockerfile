FROM python:3.10
ENV PIP_ROOT_USER_ACTION=ignore

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

ENV PYTHONPATH "${PYTHONPATH}:/"
WORKDIR /app

RUN apt-get update
RUN apt-get -y install curl

RUN curl https://packages.microsoft.com/keys/microsoft.asc | tee /etc/apt/trusted.gpg.d/microsoft.asc
RUN curl https://packages.microsoft.com/config/debian/11/prod.list >  /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update

RUN ACCEPT_EULA=Y apt-get install -y msodbcsql17
RUN apt-get install -y unixodbc-dev



RUN pip install --upgrade pip 

RUN pip install pyodbc && pip install poetry && pip install itsdangerous

RUN pip install pip-tools
COPY . /app/
RUN ls -l /app
RUN pip install -r requirements.txt 

RUN pip install gunicorn
RUN apt-get clean


EXPOSE 8000

ARG CLIENT_ID
ENV CLIENT_ID=$CLIENT_ID

ARG OID_CONF_URLOID_CONF_URL
ENV OID_CONF_URLOID_CONF_URL=$OID_CONF_URLOID_CONF_URL

ARG environment
ENV environment=$environment

ARG ACCESS_TOKEN
ENV ACCESS_TOKEN=$ACCESS_TOKEN

ARG TENANT_ID
ENV TENANT_ID=$TENANT_ID

ARG APP_ID
ENV APP_ID=$APP_ID

ARG AUTHORITY
ENV AUTHORITY=$AUTHORITY

ARG DB_SERVER
ENV DB_SERVER=$DB_SERVER

ARG DB_NAME
ENV DB_NAME=$DB_NAME

ARG DB_USERNAME
ENV DB_USERNAME=$DB_USERNAME

ARG DB_PASSWORD
ENV DB_PASSWORD=$DB_PASSWORD

ARG DB_DRIVER
ENV DB_DRIVER=$DB_DRIVER

ARG AZURE_CLIENT_ID
ENV AZURE_CLIENT_ID=$AZURE_CLIENT_ID

ARG AZURE_CLIENT_SECRET
ENV AZURE_CLIENT_SECRET=$AZURE_CLIENT_SECRET

ARG AZURE_STORAGE_ACCOUNT_URL
ENV AZURE_STORAGE_ACCOUNT_URL=$AZURE_STORAGE_ACCOUNT_URL

ARG AZURE_STORAGE_CONTAINER_NAME
ENV AZURE_STORAGE_CONTAINER_NAME=$AZURE_STORAGE_CONTAINER_NAME

CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "main:app", "--bind", "0.0.0.0:8000", "--timeout", "300000"]
