from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from uuid import UUID


class UserBase(BaseModel):
    UserLogin: str = Field(max_length=50)
    UserFirstName: Optional[str] = Field(default=None, max_length=50)
    UserLastName: Optional[str] = Field(default=None, max_length=50)
    UserFullName: Optional[str] = Field(default=None, max_length=50)
    UserEmail: Optional[str] = Field(default=None, max_length=255)
    MarsUsername: Optional[str] = Field(default=None, max_length=50)
    UserType: Optional[str] = Field(default=None, max_length=50)
    ManagerID: Optional[UUID] = Field(default=None)
    Active: bool = Field(default=True)
    IsLockedOut: bool = Field(default=False)
    # IsPassReset: bool = Field(default=False)


class UserCreate(UserBase):
    pass
    # UserPass: str = Field(max_length=50)


class UserUpdate(BaseModel):
    UserLogin: Optional[str] = Field(default=None, max_length=50)
    UserFirstName: Optional[str] = Field(default=None, max_length=50)
    UserLastName: Optional[str] = Field(default=None, max_length=50)
    UserFullName: Optional[str] = Field(default=None, max_length=50)
    UserEmail: Optional[str] = Field(default=None, max_length=255)
    MarsUsername: Optional[str] = Field(default=None, max_length=50)
    UserType: Optional[str] = Field(default=None, max_length=50)
    ManagerID: Optional[UUID] = Field(default=None)
    Active: Optional[bool] = Field(default=None)
    IsLockedOut: bool = Field(default=False)
    # IsPassReset: bool = Field(default=False)


class UserResponse(UserBase):
    UserID: UUID
    UserFullName: Optional[str]
    IsLockedOut: bool
    LastLoginDate: Optional[datetime]
    CreateDate: datetime
    roles: list["RoleResponse"]

    class Config:
        from_attributes = True


class RoleBase(BaseModel):
    RoleName: str = Field(max_length=50)
    RoleDescription: Optional[str] = Field(default=None, max_length=100)


class RoleCreate(RoleBase):
    pass


class RoleUpdate(BaseModel):
    RoleName: Optional[str] = Field(default=None, max_length=50)
    RoleDescription: Optional[str] = Field(default=None, max_length=100)


class RoleResponse(RoleBase):
    RoleID: UUID

    class Config:
        from_attributes = True


class UserRoleCreate(BaseModel):
    UserID: Optional[UUID]
    RoleID: Optional[UUID]


class UserRoleResponse(BaseModel):
    UserID: UUID
    RoleID: UUID

    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    UserID: UUID
    UserLogin: str
    UserFullName: Optional[str]

    class Config:
        orm_mode = True


class PaginatedUserListsResponse(BaseModel):
    userList: List[UserResponse]
    totalCount: int
