import pandas as pd
import re
from datetime import datetime
from fastapi import UploadFile, HTTPException
from typing import Dict, List, Optional, Set, Union, Tuple
import io
import csv
import chardet
import os
from tempfile import NamedTemporaryFile
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>ill
from openpyxl.utils.dataframe import dataframe_to_rows
from services.custom_upload import AzureBlobService
from config.constants import (
    VALID_EXTENSIONS,
    DISTRIBUTOR_NAMES,
    FILE_TYPE_MAPPINGS,
    FILENAME_PATTERNS,
    FIXED_WIDTH_SCHEMA,
    COLUMN_SCHEMA,
    MIN_COLUMNS,
)

blob_service = AzureBlobService()


class FileValidator:
    """Enhanced file validator with comprehensive validation rules and error reporting"""

    def __init__(self):
        self.errors: List[Dict[str, str]] = []
        self.warnings: List[Dict[str, str]] = []
        self.validated_data: Optional[pd.DataFrame] = None
        self.detected_delimiter: Optional[str] = None
        self.error_file_path: Optional[str] = None
        self.blob_service = blob_service or AzureBlobService()
        self.error_blob_url: Optional[str] = None

    async def convert_pfp_fixed_width_to_csv(self, file: UploadFile) -> bytes:
        """
        Convert PFP fixed-width TXT file to CSV format
        """
        try:
            # Detect encoding
            file_content = await file.read()
            file.file.seek(0)
            encoding = chardet.detect(file_content)["encoding"] or "utf-8"
            text_content = file_content.decode(encoding)

            # Determine file type from content (sales, inventory, or store mapping)
            file_type = self.determine_pfp_file_type(text_content)

            # Get the appropriate schema
            schema = FIXED_WIDTH_SCHEMA["PFP"].get(file_type)
            if not schema:
                raise ValueError(f"No schema defined for PFP {file_type} files")

            # Read fixed-width file using the schema
            colspecs = [
                (col["start"] - 1, col["start"] - 1 + col["width"]) for col in schema
            ]
            names = [col["name"] for col in schema]

            # Check if first line looks like headers (optional)
            first_line = text_content.split("\n")[0].strip()
            skiprows = 1 if any(name in first_line for name in names) else 0

            df = pd.read_fwf(
                io.StringIO(text_content),
                colspecs=colspecs,
                names=names,
                dtype=str,
                skiprows=skiprows,  # Skip header row if it exists
            )

            # Convert to CSV
            output = io.StringIO()
            df.to_csv(output, index=False)
            return output.getvalue().encode("utf-8")

        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to convert PFP fixed-width file to CSV: {str(e)}",
            )

    def determine_pfp_file_type(self, content: str) -> str:
        """
        Determine PFP file type by analyzing the content structure
        """
        # Sample the first few lines to analyze
        lines = content.splitlines()[:10]

        # Check for store mapping file indicators
        if any("StoreName" in line for line in lines) and any(
            "Address" in line for line in lines
        ):
            return "store_mapping"

        # Check for inventory file indicators
        if any("InvDT" in line for line in lines) and any(
            "QTY" in line for line in lines
        ):
            return "inventory"

        # Default to sales file
        return "sales"

    async def validate_file(
        self, file: UploadFile
    ) -> Dict[str, Union[bool, List, int]]:
        """Comprehensive file validation including structure, content, and data quality"""
        self._reset_validation_state()

        # Step 1: Basic file validation
        if not self._validate_file_basics(file):
            return self._validation_result()

        # Step 2: Check if file needs headers added (SAR or ANP files)
        needs_headers = "SAR" in file.filename or "ANP" in file.filename

        # Step 3: Parse filename
        filename_data = self._parse_filename(file.filename)
        if not filename_data:
            self._add_error(
                "Invalid filename", "Filename doesn't match expected pattern"
            )
            return self._validation_result()

        file_type = None
        if filename_data and filename_data["file_type"]:
            file_type = self._determine_file_type(filename_data["file_type"])

        distributor = filename_data["distributor"]

        try:
            content = await file.read()

            if needs_headers:
                # Determine file type (store or sales) based on filename
                if "store" in file.filename.lower():
                    file_type = "store_mapping"
                else:
                    file_type = "sales"

                # Add appropriate headers
                content = self._add_headers_to_file(content, file.filename, file_type)

                # Create a new file-like object with the modified content
                file.file = io.BytesIO(content)
                file.size = len(content)

            if distributor == "PFP" and file.filename.lower().endswith(".txt"):
                # Handle PFP fixed-width files
                df = self._read_fixed_width_file(content, distributor, file_type)
            elif file.filename.lower().endswith(".txt"):
                # For text files, convert to structured format first
                structured_content, _, _ = self._convert_txt_to_dataframe(content)
                df = pd.read_csv(io.StringIO(structured_content))
            else:
                # For other files, read normally
                df = self._read_file_content(file)

            self.validated_data = df

            # If file type wasn't determined from filename, try from content
            if not file_type:
                file_type = self._determine_file_type_from_content(df)
                if file_type:
                    self._add_warning(
                        "File type inferred", "File type was determined from content"
                    )
                else:
                    self._add_error(
                        "Unknown file type",
                        "Cannot determine file type from filename or content",
                    )

            # Validate content if file type is known
            if file_type:
                self._validate_content(df, file_type)

                # Generate error file if there are data validation issues
                if self.errors:
                    self.error_file_path = self._generate_error_file(df, file.filename)
                    if self.error_file_path:
                        # Upload error file to blob storage
                        with open(self.error_file_path, "rb") as error_file:
                            error_blob_url = (
                                await self.blob_service.upload_file_from_bytes(
                                    error_file.read(),
                                    os.path.basename(self.error_file_path),
                                    "NORTH_AMERICA/DEMAND/NPDDC/",
                                )
                            )
                            self.error_blob_url = error_blob_url
                        # Remove local error file
                        os.remove(self.error_file_path)

            return self._validation_result()

        except Exception as e:
            self._add_error("File read error", f"Could not read file: {str(e)}")
            return self._validation_result()

    def _read_fixed_width_file(
        self, content: bytes, distributor: str, file_type: str
    ) -> pd.DataFrame:
        """Read fixed-width file based on the schema"""
        try:
            # Detect encoding
            encoding = self._detect_encoding(content)
            text_content = content.decode(encoding)

            # Get the column definitions for this file type
            colspecs = self._get_fixed_width_colspecs(distributor, file_type)
            if not colspecs:
                raise ValueError(
                    f"No fixed-width schema defined for {distributor} {file_type} files"
                )

            # Read the fixed-width file
            df = pd.read_fwf(
                io.StringIO(text_content),
                colspecs=[
                    (col["start"] - 1, col["start"] - 1 + col["width"])
                    for col in colspecs
                ],
                names=[col["name"] for col in colspecs],
                dtype=str,  # Read all as strings initially
            )

            return df

        except Exception as e:
            raise ValueError(f"Failed to read fixed-width file: {str(e)}")

    def _get_fixed_width_colspecs(self, distributor: str, file_type: str) -> List[Dict]:
        """Get the column specifications for fixed-width files"""
        return FIXED_WIDTH_SCHEMA.get(distributor, {}).get(file_type, [])

    def _add_headers_to_file(
        self, content: bytes, filename: str, file_type: str
    ) -> bytes:
        """Add appropriate headers to SAR or ANP files based on their type"""
        try:
            # Detect encoding
            encoding = self._detect_encoding(content)
            text_content = content.decode(encoding)

            # Detect delimiter
            delimiter = self._detect_delimiter(text_content)
            if not delimiter:
                delimiter = ","  # default to comma if detection fails

            # Determine the appropriate headers
            headers = self._get_headers_for_file(filename, file_type)

            # Split the content into lines
            lines = text_content.splitlines()

            # Add headers as the first line
            header_line = delimiter.join(headers)
            lines.insert(0, header_line)

            # Rebuild the content with headers
            new_content = "\n".join(lines)

            return new_content.encode(encoding)

        except Exception as e:
            raise ValueError(f"Failed to add headers to file: {str(e)}")

    def _get_headers_for_file(self, filename: str, file_type: str) -> List[str]:
        """Return the appropriate headers based on filename and file type"""
        if "SAR" in filename:
            if file_type == "store_mapping":
                return [
                    "StoreID",
                    "StoreName",
                    "Address",
                    "City",
                    "State",
                    "Zip",
                    "Phone",
                    "Dist Rep",
                ]
            else:  # sales
                return [
                    "StoreID",
                    "StoreName",
                    "DistWarehouse",
                    "DistRep",
                    "UPC",
                    "Description",
                    "TranDt",
                    "UOMCnt",
                    "UOM",
                    "Qty",
                    "TotalSales",
                ]
        elif "ANP" in filename:
            if file_type == "store_mapping":
                return [
                    "StoreName",
                    "StoreID",
                    "Address",
                    "City",
                    "Province",
                    "ZipCode",
                    "Phone",
                    "Dist Rep",
                ]
            else:  # sales
                return [
                    "StoreID",
                    "StoreName",
                    "Dist Rep",
                    "UPC",
                    "Description",
                    "TranDt",
                    "Qty",
                    "UOM",
                    "UOMCnt",
                    "TotalSales",
                ]
        else:
            raise ValueError("Headers only added for SAR or ANP files")

    def _reset_validation_state(self) -> None:
        """Reset validation state for new file"""
        self.errors = []
        self.warnings = []
        self.validated_data = None
        self.error_file_path = None

    def _validate_file_basics(self, file: UploadFile) -> bool:
        """Validate basic file properties"""
        if not any(file.filename.lower().endswith(ext) for ext in VALID_EXTENSIONS):
            self._add_error(
                "Invalid file type",
                f"Supported formats: {', '.join(VALID_EXTENSIONS)}",
            )
            return False
        return True

    def _parse_filename(self, filename: str) -> Optional[Dict[str, str]]:
        """Parse and validate filename structure with comprehensive pattern matching"""

        pattern = re.compile(
            r"^(?:(?P<date>\d{4}-\d{2}-\d{2})_)?"  # Optional YYYY-MM-DD_
            r"(?:(?P<time>\d{3,4}(AM|PM))_)?"  # Optional HHMM(AM|PM)_
            r"(?P<distributor>[A-Za-z]+)"  # Distributor (required)
            r"(?P<file_type_number>\d*)"  # Optional file type number
            r"(?:_(?P<file_type>[A-Za-z]+))?"  # Optional _filetype
            r"(?:[ _]+(?P<file_type2>[A-Za-z]+))?"  # Optional space filetype
            r"(?:\s+(?P<product>[A-Za-z0-9]+\s*[A-Za-z0-9]*))?"  # Optional product info
            r"(?:\s+(?P<date_info>[\d\-\.]+))?"  # Optional date info
            r"(?:[ _](?P<date_variant>\d{4}[_\-]\d{1,2}[_\-]\d{1,2}|\d{1,2}[_\-]\d{1,2}[_\-]\d{2,4}))?"  # Date variants
            r"(?:[ _](?P<date_range>\d{6}(-\d{6})?))?"  # Optional date range
            r"(?P<extension>\.\w+)$",  # Extension
            re.IGNORECASE,
        )

        # Alternative pattern for files without file type (default to sales)

        alt_pattern = re.compile(
            r"^(?:(?P<date>\d{4}-\d{2}-\d{2})_)?"  # Optional YYYY-MM-DD_
            r"(?:(?P<time>\d{3,4}(AM|PM))_)?"  # Optional HHMM(AM|PM)_
            r"(?P<distributor>[A-Za-z]+)"  # Distributor
            r"(?P<extension>\.\w+)$",  # Extension
            re.IGNORECASE,
        )

        match = pattern.match(filename)

        if not match:
            # Try the alternative pattern for files without file type

            match = alt_pattern.match(filename)

            if match:
                # Default to 'sales' if no file type specified

                return {
                    "date": match.group("date") or "",
                    "time": match.group("time") or "",
                    "distributor": match.group("distributor"),
                    "file_type": "sales",  # Default value
                    "extension": match.group("extension"),
                }

            return None

        # Determine file type from various possible groups

        file_type = (
            match.group("file_type")
            or match.group("file_type_number")
            or match.group("file_type2")
            or ""
        ).lower()

        if file_type.isdigit():
            file_type_mapping = {"1": "store_mapping", "2": "sales", "3": "inventory"}

            file_type = file_type_mapping.get(file_type, file_type)

        return {
            "date": match.group("date") or "",
            "time": match.group("time") or "",
            "distributor": match.group("distributor"),
            "file_type": file_type
            if file_type
            else "sales",  # Default to sales if empty
            "product_info": match.group("product") or "",
            "date_info": match.group("date_info") or "",
            "date_variant": match.group("date_variant") or "",
            "date_range": match.group("date_range") or "",
            "extension": match.group("extension"),
        }

    def _determine_file_type(self, indicator: str) -> Optional[str]:
        """Determine file type from filename indicator"""
        indicator = indicator.lower()
        for file_type, aliases in FILE_TYPE_MAPPINGS.items():
            if any(alias in indicator for alias in aliases):
                return file_type
        return None

    def _determine_file_type_from_content(self, df: pd.DataFrame) -> Optional[str]:
        """Attempt to determine file type by analyzing column patterns"""
        columns_lower = {
            col.lower() for col in df.columns
        }  # Use a set for faster lookups

        # Check for sales file indicators
        sales_keywords = {
            "sales",
            "amount",
            "transaction",
            "total",
            "price",
            "shipto_code",
            "customer_id",
        }
        if any(keyword in col for col in columns_lower for keyword in sales_keywords):
            return "sales"

        # Check for inventory indicators
        inventory_keywords = {
            "inventory",
            "stock",
            "qty",
            "quantity",
            "warehouse_id",
            "dc name",
        }
        if any(
            keyword in col for col in columns_lower for keyword in inventory_keywords
        ):
            return "inventory"

        # Check for store mapping indicators
        store_keywords = {"store", "location", "address", "storeid"}
        if any(keyword in col for col in columns_lower for keyword in store_keywords):
            return "store_mapping"

        return None

    def _convert_txt_to_dataframe(self, content: bytes) -> Tuple[str, str, str]:
        """
        Convert text file content to structured DataFrame with delimiter detection
        Returns: (csv_content, detected_delimiter, encoding)
        """
        try:
            # Detect encoding
            encoding = self._detect_encoding(content)
            text_content = content.decode(encoding)

            # Detect delimiter
            self.detected_delimiter = self._detect_delimiter(text_content)
            if not self.detected_delimiter:
                raise ValueError("Could not detect delimiter in text file")

            # Read into DataFrame to validate structure
            df = pd.read_csv(
                io.StringIO(text_content),
                delimiter=self.detected_delimiter,
                engine="python",
            )

            if df.empty:
                raise ValueError("File appears to be empty after parsing")

            # Convert back to CSV for validation
            output = io.StringIO()
            df.to_csv(output, index=False)
            csv_content = output.getvalue()
            return csv_content, self.detected_delimiter, encoding

        except Exception as e:
            raise HTTPException(
                status_code=400, detail=f"Failed to convert text file: {str(e)}"
            )

    def _detect_encoding(self, content: bytes) -> str:
        """Detect file encoding with fallbacks"""
        try:
            # Try UTF-8 first
            content.decode("utf-8")
            return "utf-8"
        except UnicodeDecodeError:
            try:
                detected = chardet.detect(content)
                return detected["encoding"] or "latin-1"
            except Exception:
                return "latin-1"

    def _detect_delimiter(self, text_content: str) -> Optional[str]:
        """Auto-detect the delimiter used in a text file"""
        # Try CSV sniffer first
        try:
            dialect = csv.Sniffer().sniff(text_content[:2048])
            return dialect.delimiter
        except:
            # Fallback to checking common delimiters
            common_delimiters = ["\t", "|", ",", ";"]
            for delim in common_delimiters:
                if delim in text_content[:2048]:
                    return delim
            return None

    def _read_file_content(self, file: UploadFile) -> pd.DataFrame:
        """Read file content based on extension with special handling for .txt"""
        file.file.seek(0)
        content = file.file.read()
        if file.filename.lower().endswith(".txt"):
            # Use our new conversion method for text files
            csv_content, _, _ = self._convert_txt_to_dataframe(content)
            return pd.read_csv(io.StringIO(csv_content))
        elif file.filename.lower().endswith(".csv"):
            return pd.read_csv(io.BytesIO(content))
        elif file.filename.lower().endswith((".xls", ".xlsx")):
            return pd.read_excel(io.BytesIO(content))
        else:
            raise ValueError("Unsupported file format")

    def _validate_content(self, df: pd.DataFrame, file_type: str) -> None:
        """Validate file content against schema"""
        schema = COLUMN_SCHEMA.get(file_type, {})

        # Check minimum columns
        expected_min_cols = MIN_COLUMNS.get(file_type, 1)
        if len(df.columns) < expected_min_cols:
            self._add_error(
                "Insufficient columns",
                f"Expected at least {expected_min_cols} columns for '{file_type}' file type, found {len(df.columns)}",
            )

        # Validate required columns
        required_columns = schema.get("required", {})
        for col_name, col_def in required_columns.items():
            self._validate_column(df, col_name, col_def, required=True)

        # Validate optional columns
        optional_columns = schema.get("optional", {})
        for col_name, col_def in optional_columns.items():
            self._validate_column(df, col_name, col_def, required=False)

    def _validate_column(
        self, df: pd.DataFrame, col_name: str, col_def: Dict, required: bool = True
    ) -> None:
        """Validate a single column against its definition and track errors"""
        # Find actual column name (checking aliases)
        actual_col = self._find_matching_column(
            df, col_name, col_def.get("aliases", [])
        )

        if not actual_col:
            if required:
                self._add_error(
                    "Missing required column",
                    f"Required column '{col_name}' or its aliases not found in the file.",
                    column_expected=col_name,
                    aliases=col_def.get("aliases", []),
                )
            return  # Cannot validate type or nulls if column isn't found

        # Initialize error tracking
        # Initialize error tracking
        error_messages = [""] * len(df)
        has_data_type_errors = False

        # Check data type and collect errors
        if "type" in col_def:
            type_errors = self._check_data_type_with_errors(
                df, actual_col, col_def["type"], col_name
            )
            if type_errors:
                has_data_type_errors = True
                error_rows = []
                for idx, msg in type_errors.items():
                    error_messages[idx] = msg
                    error_rows.append(idx + 1)  # Convert to 1-based row numbers

                # Add data type error to the error list
                self._add_error(
                    "Invalid data type",
                    f"Column '{actual_col}' (expected '{col_name}') has {len(type_errors)} "
                    f"values with incorrect data type. Expected type: {col_def['type']}",
                    column_actual=actual_col,
                    rows=error_rows,
                    error_count=len(type_errors),
                    expected_type=col_def["type"],
                )

        # # Check data type and collect errors
        # type_errors = self._check_data_type_with_errors(
        #     df, actual_col, col_def["type"], col_name
        # )
        # for idx, msg in type_errors.items():
        #     error_messages[idx] = msg

        # Check for null values in required columns
        if required:
            null_mask = df[actual_col].isnull()
            if null_mask.any():
                null_count = null_mask.sum()
                null_rows = [
                    i + 1 for i in null_mask[null_mask].index
                ]  # Convert to 1-based row numbers

                self._add_error(
                    "Null values found in required column",
                    f"Column '{actual_col}' (expected '{col_name}') has {null_count} null values. Required columns cannot have nulls.",
                    column_actual=actual_col,
                    rows=null_rows,
                    null_count=null_count,
                )
                for idx in null_mask[null_mask].index:
                    if error_messages[idx]:
                        error_messages[idx] += " | "
                    error_messages[idx] += (
                        f"row {idx + 1}: Null value in required column"
                    )

        # Add error messages to the dataframe if any were found
        if any(msg for msg in error_messages):
            error_col_name = f"{actual_col}_Error"
            df[error_col_name] = error_messages

    def _check_data_type_with_errors(
        self,
        df: pd.DataFrame,
        col_name: str,
        expected_types: Union[str, List[str]],
        original_name: str,
    ) -> Dict[int, str]:
        """Validate column data type and return error messages for invalid rows"""
        col_data = df[col_name]
        error_messages = {}

        # Ensure expected_types is always a list for consistent handling
        if isinstance(expected_types, str):
            expected_types = [expected_types]

        # Convert to set for easier comparison
        expected_types_set = set(expected_types)

        # Check each value against allowed types
        for idx, val in col_data.items():
            if pd.isna(val):
                continue  # Skip null values (handled separately in null check)

            valid = False
            current_errors = []
            actual_type = type(val).__name__

            # Check against all expected types
            if "string" in expected_types_set:
                if isinstance(val, str):
                    valid = True
                else:
                    current_errors.append(f"expected string, got {actual_type}")

            if "numeric" in expected_types_set and not valid:
                try:
                    pd.to_numeric(val)
                    valid = True
                except (ValueError, TypeError):
                    current_errors.append(f"expected numeric, got {actual_type}")

            if "date" in expected_types_set and not valid:
                try:
                    if re.fullmatch(r"\d{7,8}", str(val).strip()):
                        pd.to_datetime(val, format="%m%d%Y")
                    else:
                        pd.to_datetime(val)
                    valid = True
                except (ValueError, TypeError):
                    current_errors.append("invalid date format")

            if not valid:
                error_messages[idx] = (
                    f"row {idx + 1}: Value '{val}' failed type validation. "
                    f"Errors: {', '.join(current_errors)}. "
                    f"Allowed types: {', '.join(expected_types)}"
                )

        return error_messages

    def _generate_error_file(self, df: pd.DataFrame, original_filename: str) -> str:
        """Generate an Excel file with error details and highlighted cells"""
        try:
            # Create a new filename for the error file
            base_name, ext = os.path.splitext(original_filename)
            error_filename = f"{base_name}_errors.xlsx"

            # Create a new workbook
            wb = Workbook()
            ws = wb.active

            # Prepare the dataframe for export
            # Combine all error columns into one "Error Detail" column
            error_cols = [col for col in df.columns if col.endswith("_Error")]
            if error_cols:
                df["Error Detail"] = df[error_cols].apply(
                    lambda row: " | ".join(filter(None, row)), axis=1
                )
                # Remove individual error columns
                df = df.drop(columns=error_cols)

            # Write dataframe to worksheet
            for r_idx, row in enumerate(
                dataframe_to_rows(df, index=False, header=True), 1
            ):
                for c_idx, value in enumerate(row, 1):
                    ws.cell(row=r_idx, column=c_idx, value=value)

            # Define fill colors
            red_fill = PatternFill(
                start_color="FF0000",
                end_color="FF0000",
                fill_type="solid",  # Bright red for data type errors
            )
            yellow_fill = PatternFill(
                start_color="FFFF00",
                end_color="FFFF00",
                fill_type="solid",  # Yellow for error detail cells
            )
            orange_fill = PatternFill(
                start_color="FFA500",
                end_color="FFA500",
                fill_type="solid",  # Orange for null values
            )

            # Find the error detail column
            error_col_idx = None
            headers = [cell.value for cell in ws[1]]  # Get header row
            for col_idx, header in enumerate(headers, 1):
                if header == "Error Detail":
                    error_col_idx = col_idx
                    break

            # Highlight cells with errors
            if error_col_idx:
                for r_idx in range(2, len(df) + 2):  # Skip header row
                    error_msg = ws.cell(row=r_idx, column=error_col_idx).value
                    if error_msg:
                        # Highlight the error detail cell in yellow
                        ws.cell(row=r_idx, column=error_col_idx).fill = yellow_fill

                        # Find which columns have errors for this row
                        error_parts = error_msg.split(" | ")
                        for part in error_parts:
                            if part.startswith("row"):
                                # Data type errors
                                if "failed type validation" in part:
                                    # Extract the column name from the cell reference
                                    # The error message format is: "row X: Value 'Y' failed type validation..."
                                    # We need to get the column that contains this value
                                    # Look for the column that has this exact value
                                    error_value = (
                                        part.split("Value '")[1].split("'")[0]
                                        if "'" in part
                                        else None
                                    )
                                    if error_value:
                                        for col_idx, header in enumerate(headers, 1):
                                            if (
                                                header != "Error Detail"
                                            ):  # Skip error detail column
                                                cell_value = ws.cell(
                                                    row=r_idx, column=col_idx
                                                ).value
                                                if str(cell_value) == error_value:
                                                    # Highlight data type errors in red
                                                    ws.cell(
                                                        row=r_idx, column=col_idx
                                                    ).fill = red_fill
                                                    break
                                # Null value errors
                                elif "Null value" in part:
                                    # Find all required columns that might be null
                                    for col_idx, header in enumerate(headers, 1):
                                        if header in COLUMN_SCHEMA.get("sales", {}).get(
                                            "required", {}
                                        ):
                                            if pd.isna(
                                                ws.cell(row=r_idx, column=col_idx).value
                                            ):
                                                # Highlight null values in orange
                                                ws.cell(
                                                    row=r_idx, column=col_idx
                                                ).fill = orange_fill

            # Auto-size columns for better readability
            for column in ws.columns:
                max_length = 0
                column = [cell for cell in column]
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2) * 1.2
                ws.column_dimensions[column[0].column_letter].width = adjusted_width

            # Save the workbook
            wb.save(error_filename)
            self._add_warning(
                "Error file generated",
                f"Validation issues found. Error details saved to {error_filename}",
                error_file_path=error_filename,
            )

            return error_filename

        except Exception as e:
            self._add_warning(
                "Error file generation failed",
                f"Could not generate error file: {str(e)}",
            )
            return ""

    def _find_matching_column(
        self, df: pd.DataFrame, primary_name: str, aliases: List[str]
    ) -> Optional[str]:
        """Find matching column in dataframe checking primary name and aliases"""
        columns_lower = {
            col.strip().lower(): col for col in df.columns
        }  # Store actual column names for lookup
        search_terms = [primary_name.lower()] + [alias.lower() for alias in aliases]

        for term in search_terms:
            if term in columns_lower:
                return columns_lower[term]  # Return the original casing column name
        return None

    def _add_error(self, error_type: str, message: str, **kwargs) -> None:
        """Add an error with detailed structure including row numbers and counts"""
        error_details = {
            "type": "error",
            "validation_check": error_type,
            "message": message,
            "comments": "",
            "action_to_perform": "Review and correct the data",
            **{k: v for k, v in kwargs.items() if k not in ["rows", "null_count"]},
        }

        # Handle row numbers properly
        if "rows" in kwargs:
            if isinstance(kwargs["rows"], str):
                # If rows is already a string, use it as is
                row_numbers = kwargs["rows"]
            else:
                # Convert row numbers to proper format
                try:
                    row_numbers = ", ".join(
                        [
                            f"row {row}" if isinstance(row, int) else str(row)
                            for row in kwargs["rows"]
                        ]
                    )
                except (TypeError, AttributeError):
                    row_numbers = str(kwargs["rows"])

            error_details["rows"] = row_numbers
            error_details["comments"] += f"Affected rows: {row_numbers}. "

        # Add counts to comments
        if "null_count" in kwargs:
            error_details["comments"] += (
                f"Null values found in {kwargs['null_count']} rows. "
            )
        if "error_count" in kwargs:
            error_details["comments"] += (
                f"Errors found in {kwargs['error_count']} rows. "
            )

        self.errors.append(error_details)

    def _add_warning(self, warning_type: str, message: str, **kwargs) -> None:
        """Add a warning to the warning list with simplified structure"""
        details = ", ".join(f"{k}: {v}" for k, v in kwargs.items())
        full_message = f"{message} ({details})" if details else message

        self.warnings.append({"type": warning_type, "message": full_message})

    def _validation_result(self) -> Dict[str, Union[bool, List, int]]:
        """Prepare final validation result"""
        return {
            "is_valid": len(self.errors) == 0,
            "errors": self.errors,
            "warnings": self.warnings,
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "error_blob_url": self.error_blob_url,
        }
