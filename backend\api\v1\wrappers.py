import time
from typing import Callable
from functools import wraps
from fastapi import HTTPException


def retry_request(max_retries: int = 5, delay: int = 5, skip_exceptions: list = [HTTPException, ]):
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            attempts = 0
            while attempts < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if isinstance(e, tuple(skip_exceptions)):
                        raise e
                    attempts += 1
                    print(f"WARNING Attempting[{attempts}] Error: {e} ")
                    if attempts < max_retries:
                        time.sleep(delay)
                    else:
                        raise e
        return wrapper
    return decorator
