import pytest
from fastapi.testclient import TestClient
from fastapi.encoders import jsonable_encoder
import sys
import os

# Adjust path to import main app
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from main import app

# Initialize test client
client = TestClient(app)
BASE_URL = "/api/v1/stores"

class TestStoreMappingMetadataUpdate:
    """Test class for store mapping metadata update API endpoint"""
    
    def test_update_metadata_success_all_fields(self):
        """Test successful update with all fields provided"""
        # Test data with all possible fields
        update_data = {
            "vcid": 1232,
            "customer_type": "Retail Chain",
            "rep": "<PERSON>",
            "address": "123 Main Street, New York, NY 10001",
            "warehouse": "East Coast Distribution Center",
            "country": "USA",
            "phone": "************"
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        # Verify response structure and success
        assert response.status_code == 200, f"Request failed: {response.text}"
        response_data = response.json()
        
        # Check response structure
        assert "success" in response_data
        assert "message" in response_data
        assert "updated_fields" in response_data
        assert "vcid" in response_data
        
        # Check response values
        assert response_data["success"] is True
        assert response_data["vcid"] == 1232
        assert "Store mapping metadata updated successfully" in response_data["message"]
        
        # Check that all fields were updated
        expected_fields = ["customer_type", "rep", "address", "warehouse", "country", "phone"]
        assert set(response_data["updated_fields"]) == set(expected_fields)
    
    def test_update_metadata_success_partial_fields(self):
        """Test successful update with only some fields provided"""
        update_data = {
            "vcid": 1232,
            "customer_type": "Independent Store",
            "phone": "************"
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        assert response.status_code == 200, f"Request failed: {response.text}"
        response_data = response.json()
        
        assert response_data["success"] is True
        assert response_data["vcid"] == 1232
        assert set(response_data["updated_fields"]) == {"customer_type", "phone"}
    
    def test_update_metadata_empty_strings_to_null(self):
        """Test that empty strings are converted to NULL"""
        update_data = {
            "vcid": 1232,
            "customer_type": "",  # Empty string should become NULL
            "rep": "   ",  # Whitespace-only should become NULL
            "address": "Valid Address"  # Valid value should remain
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        assert response.status_code == 200, f"Request failed: {response.text}"
        response_data = response.json()
        
        assert response_data["success"] is True
        assert set(response_data["updated_fields"]) == {"customer_type", "rep", "address"}
    
    def test_update_metadata_missing_vcid(self):
        """Test validation error when VCID is missing"""
        update_data = {
            "customer_type": "Retail Chain"
            # Missing vcid
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        # Should return 422 for validation error (missing required field)
        assert response.status_code == 422
    
    def test_update_metadata_invalid_vcid_type(self):
        """Test validation error when VCID is not an integer"""
        update_data = {
            "vcid": "not_an_integer",
            "customer_type": "Retail Chain"
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        # Should return 422 for validation error (invalid type)
        assert response.status_code == 422
    
    def test_update_metadata_no_fields_to_update(self):
        """Test error when no fields are provided for update"""
        update_data = {
            "vcid": 1232
            # No other fields provided
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        # Should return 400 for bad request (no fields to update)
        assert response.status_code == 400
        response_data = response.json()
        assert "At least one field must be provided for update" in response_data["detail"]
    
    def test_update_metadata_record_not_found(self):
        """Test 404 error when VCID doesn't exist"""
        update_data = {
            "vcid": 999999,  # Non-existent VCID
            "customer_type": "Retail Chain"
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        # Should return 404 for not found
        assert response.status_code == 404
        response_data = response.json()
        assert "Store mapping record with VCID 999999 not found" in response_data["detail"]
    
    def test_update_metadata_field_length_validation(self):
        """Test handling of very long field values"""
        update_data = {
            "vcid": 1232,
            "customer_type": "A" * 1000,  # Very long string
            "address": "B" * 2000  # Very long address
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        # Should still succeed (database will handle truncation if needed)
        # or return appropriate error based on database constraints
        assert response.status_code in [200, 400, 500]
    
    def test_update_metadata_special_characters(self):
        """Test handling of special characters in field values"""
        update_data = {
            "vcid": 1232,
            "customer_type": "Café & Restaurant's Supply Co.",
            "address": "123 Main St., Apt #4B, New York, NY 10001",
            "phone": "+**************** ext. 890"
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        assert response.status_code == 200, f"Request failed: {response.text}"
        response_data = response.json()
        assert response_data["success"] is True
    
    def test_update_metadata_response_schema(self):
        """Test that response matches expected schema"""
        update_data = {
            "vcid": 1232,
            "customer_type": "Test Store"
        }
        
        response = client.put(f"{BASE_URL}/mapping_unmapping/update_metadata", json=update_data)
        
        assert response.status_code == 200
        response_data = response.json()
        
        # Verify all required fields are present
        required_fields = ["success", "message", "updated_fields", "vcid"]
        for field in required_fields:
            assert field in response_data, f"Missing required field: {field}"
        
        # Verify field types
        assert isinstance(response_data["success"], bool)
        assert isinstance(response_data["message"], str)
        assert isinstance(response_data["updated_fields"], list)
        assert isinstance(response_data["vcid"], int)
        
        # Verify updated_fields contains strings
        for field in response_data["updated_fields"]:
            assert isinstance(field, str)
